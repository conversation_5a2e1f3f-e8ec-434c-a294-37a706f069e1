# PLANNING.md: Arquitectura para el Servidor MCP de Diseño de UI

## 1. Visión del Proyecto

Construir un servidor MCP auto-alojado que exponga un **agente de diseño de UI avanzado**. Este agente no solo generará código, sino que actuará como un socio creativo, utilizando inspiración externa y principios de diseño modernos para crear interfaces únicas y de alta calidad. El objetivo es superar los diseños genéricos de IA a través de un proceso estructurado de inspiración, sistematización y composición.

## 2. Arquitectura del Sistema

El sistema se compondrá de un único servicio Docker que aloja el servidor MCP y su lógica de agente interna.

**Componentes Clave:**

1. **`server.py` (Capa de Comunicación MCP):**
    - Responsabilidad: Gestionar las conexiones MCP, manejar las sesiones de usuario y actuar como interfaz entre el cliente (IDE) y el núcleo del agente.
    - Recibirá los prompts del usuario y los pasará al agente LangGraph.
    - Hará streaming de las respuestas y los pasos intermedios del agente de vuelta al cliente.

2. **`graph.py` (Núcleo del Agente - LangGraph):**
    - Responsabilidad: Orquestar el flujo de trabajo de diseño. Contendrá la lógica de estados del agente.
    - Su "cerebro" será la API de Gemini Pro.
    - Seguirá las directrices de `RULES.md` para tomar decisiones.

3. **`tools.py` (Capacidades del Agente):**
    - Responsabilidad: Proveer al agente de habilidades para interactuar con el mundo exterior. Estas serán las herramientas que el grafo de LangGraph podrá invocar.
    - Herramientas iniciales:
        - `browse_web_for_inspiration(query: str)`: Realiza una búsqueda en la web (usando una API como Tavily o SearxNG) para encontrar sitios de diseño.
        - `analyze_image_content(image_url: str)`: Simula "tomar un pantallazo". Utilizará un modelo multimodal (como Gemini Pro Vision) para analizar una imagen de un diseño y extraer su paleta de colores, estructura y estilo.

4. **`state.py` (Memoria del Agente):**
    - Responsabilidad: Definir la estructura de datos (`AgentState`) que persistirá a través de los diferentes pasos del grafo. Contendrá la petición del usuario, el sistema de diseño generado, el historial de feedback, etc.

## 3. Stack Tecnológico

- **Framework del Agente**: LangGraph
- **LLM**: Google Gemini Pro (con capacidad de Visión para el análisis de imágenes)
- **Protocolo**: MCP (usando `mcp-sdk` de Python)
- **Base de Código**: Fork de `punkpeye/awesome-mcp-servers` como esqueleto.
- **Despliegue**: Docker y Docker Compose.

## 4. Flujo de Trabajo del Agente (LangGraph Nodes)

1. **`analyze_request`**: Interpreta la petición inicial. Si incluye una solicitud de inspiración, activa el siguiente nodo.
2. **`research_inspiration`**: Utiliza la herramienta `browse_web_for_inspiration` y `analyze_image_content` para recopilar ideas y análisis visual.
3. **`create_design_system`**: Sintetiza la inspiración en un `design_system.json`. Este es un paso crítico y obligatorio.
4. **`generate_component_code`**: Genera el código JSX/CSS basándose en el `design_system.json` y los componentes premium.
5. **`await_and_process_feedback`**: Presenta el resultado al usuario y entra en un ciclo de refinamiento hasta recibir la aprobación final.
