"""
Simple test to verify basic functionality without external dependencies.
"""

import sys
import os

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """Test that all modules can be imported."""
    print("Testing imports...")
    
    try:
        from state import AgentState, DesignSystem, SessionManager
        print("✅ state.py imported successfully")
    except Exception as e:
        print(f"❌ Error importing state.py: {e}")
        return False
    
    try:
        # Test basic state creation
        session_manager = SessionManager()
        state = session_manager.create_session("test-123", "Create a button")
        print(f"✅ AgentState created: {state.session_id}")
    except Exception as e:
        print(f"❌ Error creating AgentState: {e}")
        return False
    
    return True

def test_basic_functionality():
    """Test basic functionality without external APIs."""
    print("\nTesting basic functionality...")
    
    try:
        from state import session_manager
        
        # Create a session
        session_id = "test-session"
        user_request = "Create a modern button component"
        
        state = session_manager.create_session(session_id, user_request)
        print(f"✅ Session created: {session_id}")
        
        # Test state updates
        state.set_phase("research_inspiration")
        state.inspiration_query = "modern button design"
        session_manager.update_session(session_id, state)
        print("✅ State updated successfully")
        
        # Test session retrieval
        retrieved_state = session_manager.get_session(session_id)
        if retrieved_state and retrieved_state.current_phase == "research_inspiration":
            print("✅ Session retrieval works")
        else:
            print("❌ Session retrieval failed")
            return False
        
        # Test feedback
        state.add_feedback("Make it bigger")
        if len(state.feedback_history) == 1:
            print("✅ Feedback system works")
        else:
            print("❌ Feedback system failed")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Error in basic functionality test: {e}")
        return False

def main():
    """Run all tests."""
    print("🧪 Simple Test Suite for UI Agent Constructor")
    print("=" * 50)
    
    tests_passed = 0
    total_tests = 2
    
    # Test 1: Imports
    if test_imports():
        tests_passed += 1
    
    # Test 2: Basic functionality
    if test_basic_functionality():
        tests_passed += 1
    
    # Summary
    print("\n" + "=" * 50)
    print(f"🏁 Test Results: {tests_passed}/{total_tests} tests passed")
    
    if tests_passed == total_tests:
        print("🎉 All basic tests passed! Core structure is working.")
        return True
    else:
        print("⚠️  Some tests failed. Check the errors above.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
