# UI Agent Constructor - API Documentation

## Overview

The UI Agent Constructor exposes its functionality through the Model Context Protocol (MCP), providing a standardized interface for AI assistants and IDEs to interact with the UI design agent.

## MCP Server Information

- **Server Name**: `ui_agent_constructor`
- **Version**: `1.0.0`
- **Protocol**: MCP (Model Context Protocol)
- **Transport**: stdio

## Available Tools

### 1. `create_ui_design`

Creates a new UI design following the Three Pillars approach: Inspiration → Systematization → Composition.

#### Input Schema

```json
{
  "type": "object",
  "properties": {
    "request": {
      "type": "string",
      "description": "Description of the UI component or interface to design",
      "required": true
    },
    "session_id": {
      "type": "string", 
      "description": "Optional session ID for continuing a design session",
      "required": false
    }
  },
  "required": ["request"]
}
```

#### Example Request

```json
{
  "request": "Create a modern SaaS landing page with dark theme and glassmorphism effects",
  "session_id": "my-landing-page-session"
}
```

#### Response Format

Returns a `TextContent` object with formatted markdown containing:

- Session information
- Current workflow phase
- Design system details (when available)
- Generated component code (when available)
- Progress indicators and next steps

#### Example Response

```markdown
# UI Design Agent - Session: `my-landing-page-session`

**Current Phase:** Research Inspiration
**Iteration:** 1

## 🎨 Gathering Design Inspiration

Searching for: *modern saas landing page dark glassmorphism*

### Found Inspiration Sources:
1. [Modern SaaS Landing](https://dribbble.com/shots/example)
   - Dark theme with glassmorphism effects

**Please review this design system and provide feedback, or approve it to proceed with code generation.**
```

---

### 2. `provide_feedback`

Provides feedback on a generated design to iterate and improve it.

#### Input Schema

```json
{
  "type": "object",
  "properties": {
    "feedback": {
      "type": "string",
      "description": "Feedback on the current design",
      "required": true
    },
    "session_id": {
      "type": "string",
      "description": "Session ID of the design to provide feedback on",
      "required": true
    }
  },
  "required": ["feedback", "session_id"]
}
```

#### Example Request

```json
{
  "feedback": "Make the colors more vibrant and add more spacing between elements",
  "session_id": "my-landing-page-session"
}
```

#### Response Format

Similar to `create_ui_design`, returns updated design information reflecting the feedback processing.

---

### 3. `get_design_status`

Gets the current status and progress of a design session.

#### Input Schema

```json
{
  "type": "object", 
  "properties": {
    "session_id": {
      "type": "string",
      "description": "Session ID to check status for",
      "required": true
    }
  },
  "required": ["session_id"]
}
```

#### Example Request

```json
{
  "session_id": "my-landing-page-session"
}
```

#### Response Format

Returns a JSON object with detailed session information:

```json
{
  "session_id": "my-landing-page-session",
  "current_phase": "await_feedback",
  "iteration": 2,
  "is_complete": false,
  "user_request": "Create a modern SaaS landing page...",
  "inspiration_complete": true,
  "design_system_approved": true,
  "created_at": "2024-01-15T10:30:00Z",
  "updated_at": "2024-01-15T10:45:00Z"
}
```

---

### 4. `list_active_sessions`

Lists all active design sessions.

#### Input Schema

```json
{
  "type": "object",
  "properties": {}
}
```

#### Example Request

```json
{}
```

#### Response Format

Returns a markdown-formatted list of all active sessions:

```markdown
## Active Design Sessions (3)

**Session:** `landing-page-001`
- Phase: await_feedback
- Iteration: 2
- Complete: false
- Request: Create a modern SaaS landing page...

**Session:** `dashboard-002`
- Phase: generate_component_code
- Iteration: 1
- Complete: false
- Request: Design an analytics dashboard...
```

## Workflow Phases

The agent progresses through several phases during the design process:

1. **`analyze_request`** - Analyzes user requirements and determines inspiration needs
2. **`research_inspiration`** - Searches web and analyzes design examples
3. **`create_design_system`** - Synthesizes inspiration into a cohesive design system
4. **`await_design_approval`** - Waits for user approval of the design system
5. **`generate_component_code`** - Creates React/JSX code based on the design system
6. **`await_feedback`** - Waits for user feedback and iterates if needed
7. **`complete`** - Design process is finished
8. **`error`** - An error occurred that requires attention

## Design System Format

The agent generates design systems in this standardized JSON format:

```json
{
  "theme": "Dark Glassmorphism",
  "colorPalette": {
    "primary": "#6366f1",
    "secondary": "#8b5cf6", 
    "accent": "#f59e0b",
    "background": "#0f172a",
    "text": "#f8fafc"
  },
  "typography": {
    "headerFont": "Inter",
    "bodyFont": "Inter",
    "baseFontSize": "16px"
  },
  "layout": {
    "gridUnit": 8,
    "borderRadius": "12px", 
    "shadows": "0 8px 32px rgba(0, 0, 0, 0.3)"
  }
}
```

## Error Handling

The API provides structured error handling with user-friendly messages:

### Common Error Types

- **`api_key_missing`** - Required API keys not configured
- **`session_not_found`** - Requested session doesn't exist
- **`invalid_input`** - Invalid or missing input parameters
- **`network_error`** - Network connectivity issues
- **`processing_failed`** - Internal processing error

### Error Response Format

```markdown
❌ **Error**: Please configure your Google AI API key in the .env file

💡 **Suggested Action**: Add GOOGLE_API_KEY=your_key_here to your .env file

🔄 This error is recoverable - you can try again.

🔍 **Error Code**: `api_key_missing`
```

## Configuration

### Required Environment Variables

- **`GOOGLE_API_KEY`** - Google AI API key for Gemini Pro (required)
- **`TAVILY_API_KEY`** - Tavily search API key (optional, enhances web search)

### Optional Environment Variables

- **`DEBUG`** - Enable debug logging (default: false)
- **`LOG_LEVEL`** - Logging level (default: INFO)

## Integration Examples

### Claude Desktop Configuration

```json
{
  "mcpServers": {
    "ui-agent-constructor": {
      "command": "python",
      "args": ["server.py"],
      "cwd": "./servers/ui_agent_constructor",
      "env": {
        "GOOGLE_API_KEY": "your_google_api_key_here",
        "TAVILY_API_KEY": "your_tavily_api_key_here"
      }
    }
  }
}
```

### VS Code MCP Extension

```json
{
  "mcp.servers": {
    "ui-agent-constructor": {
      "command": "python",
      "args": ["./servers/ui_agent_constructor/server.py"],
      "env": {
        "GOOGLE_API_KEY": "${env:GOOGLE_API_KEY}",
        "TAVILY_API_KEY": "${env:TAVILY_API_KEY}"
      }
    }
  }
}
```

## Rate Limits and Quotas

The server respects the rate limits of underlying APIs:

- **Google AI (Gemini)**: Varies by plan, typically 60 requests/minute
- **Tavily Search**: Varies by plan, typically 1000 searches/month

The agent implements graceful degradation when rate limits are hit.

## Best Practices

### 1. Session Management
- Use descriptive session IDs for better organization
- Sessions persist in memory until server restart
- Clean up completed sessions periodically

### 2. Request Optimization
- Be specific in design requests for better results
- Include context about target audience and use case
- Mention preferred design styles or inspirations

### 3. Feedback Loop
- Provide specific, actionable feedback
- Use iterative refinement for complex designs
- Approve design systems before proceeding to code generation

### 4. Error Recovery
- Check error codes for appropriate handling
- Retry recoverable errors after addressing the cause
- Monitor API key quotas and usage

## Support and Troubleshooting

### Common Issues

1. **"Google AI API key not configured"**
   - Solution: Add valid `GOOGLE_API_KEY` to environment

2. **"Session not found"**
   - Solution: Use `list_active_sessions` to find valid session IDs

3. **"Web inspiration search may be limited"**
   - Solution: Add `TAVILY_API_KEY` for enhanced search capabilities

### Debug Mode

Enable debug mode for detailed logging:

```bash
export DEBUG=true
export LOG_LEVEL=DEBUG
python server.py
```

### Log Files

Logs are written to the `logs/` directory with daily rotation:
- `logs/ui_agent_main_YYYYMMDD.log`
- `logs/ui_agent_server_YYYYMMDD.log`
- `logs/ui_agent_tools_YYYYMMDD.log`
