"""
Core agent logic for the UI Design Agent.
Implements the Three Pillars workflow from RULES.md using Google Gemini.
"""

import os
import json
from typing import Dict, Any

import google.generativeai as genai

from state import AgentState, DesignSystem
from tools import web_inspiration_tool, image_analysis_tool, design_system_generator
from logger import get_logger
from error_handler import handle_error, create_error_response

# Setup logging for graph module
logger = get_logger("graph")


class UIDesignAgent:
    """
    Main agent class that orchestrates the UI design workflow.
    Follows the Three Pillars approach: Inspiration -> Systematization -> Composition
    """

    def __init__(self):
        # Configure Google AI
        if os.getenv("GOOGLE_API_KEY"):
            genai.configure(api_key=os.getenv("GOOGLE_API_KEY"))
            self.model = genai.GenerativeModel('gemini-1.5-flash')
        else:
            self.model = None

    async def process_request(self, state: AgentState) -> AgentState:
        """
        Main entry point for processing a design request.
        Routes to appropriate workflow phase based on current state.
        """
        logger.info("Processing agent request", {
            "session_id": state.session_id,
            "current_phase": state.current_phase,
            "iteration": state.current_iteration
        })

        if not self.model:
            logger.error("Google AI model not configured", {"session_id": state.session_id})
            state.set_phase("error")
            return state

        # Route based on current phase
        if state.current_phase == "analyze_request":
            return await self._analyze_request(state)
        elif state.current_phase == "research_inspiration":
            return await self._research_inspiration(state)
        elif state.current_phase == "create_design_system":
            return await self._create_design_system(state)
        elif state.current_phase == "generate_component_code":
            return await self._generate_component_code(state)
        elif state.current_phase == "await_design_approval":
            return await self._await_design_approval(state)
        elif state.current_phase == "await_feedback":
            return await self._await_feedback(state)
        else:
            # Unknown phase, reset to analyze
            logger.warning("Unknown phase, resetting to analyze_request", {
                "session_id": state.session_id,
                "unknown_phase": state.current_phase
            })
            state.set_phase("analyze_request")
            return await self._analyze_request(state)

    async def _analyze_request(self, state: AgentState) -> AgentState:
        """
        Pilar 1 - Step 1: Analyze the user request and determine inspiration needs.
        """
        try:
            analysis_prompt = f"""
            Analyze this UI design request and determine what kind of inspiration would be most helpful:

            USER REQUEST: {state.user_request}

            Based on this request, provide:
            1. A specific search query for finding design inspiration (be specific about style, type, industry)
            2. The type of component/interface being requested (e.g., "landing_page", "dashboard", "form", "navigation")
            3. Any specific design preferences mentioned by the user

            Respond in JSON format:
            {{
                "inspiration_query": "specific search query",
                "component_type": "type of component",
                "user_preferences": "any specific preferences mentioned"
            }}
            """

            response = self.model.generate_content(analysis_prompt)

            # Parse response
            try:
                response_text = response.text
                if "```json" in response_text:
                    json_start = response_text.find("```json") + 7
                    json_end = response_text.find("```", json_start)
                    json_text = response_text[json_start:json_end].strip()
                else:
                    json_text = response_text

                analysis = json.loads(json_text)

                # Update state
                state.inspiration_query = analysis.get("inspiration_query", "modern ui design")
                state.component_type = analysis.get("component_type", "interface")

                # Move to inspiration research phase
                state.set_phase("research_inspiration")

            except json.JSONDecodeError:
                # Fallback if JSON parsing fails
                state.inspiration_query = f"modern {state.user_request} ui design"
                state.component_type = "interface"
                state.set_phase("research_inspiration")

        except Exception as e:
            error_info = handle_error(e, {"phase": "analyze_request"}, state.session_id)
            logger.error(f"Error in analyze_request: {error_info.message}")

            # Set fallback values
            state.inspiration_query = "modern ui design inspiration"
            state.set_phase("research_inspiration")

        return state

    async def _research_inspiration(self, state: AgentState) -> AgentState:
        """
        Pilar 1 - Step 2: Gather and analyze design inspiration.
        """
        try:
            # Step 1: Search for inspiration
            inspiration_sources = await web_inspiration_tool.browse_web_for_inspiration(
                state.inspiration_query, max_results=3
            )
            state.inspiration_sources = inspiration_sources

            # Step 2: Analyze images from inspiration sources
            analyses = []
            for source in inspiration_sources[:2]:  # Analyze top 2 sources
                try:
                    analysis = await image_analysis_tool.analyze_image_content(source.url)
                    if "error" not in analysis:
                        source.analysis = analysis
                        analyses.append(analysis)
                except Exception as e:
                    print(f"Error analyzing {source.url}: {e}")
                    continue

            if analyses:
                state.inspiration_complete = True
                state.set_phase("create_design_system")
            else:
                # If no successful analyses, create a fallback
                state.inspiration_complete = True
                state.set_phase("create_design_system")

        except Exception as e:
            error_info = handle_error(e, {"phase": "research_inspiration"}, state.session_id)
            logger.error(f"Error in research_inspiration: {error_info.message}")

            # Continue with fallback - mark inspiration as complete
            state.inspiration_complete = True
            state.set_phase("create_design_system")

        return state

    async def _create_design_system(self, state: AgentState) -> AgentState:
        """
        Pilar 2: Create a design system from inspiration analysis.
        """
        try:
            # Collect all analyses
            analyses = []
            for source in state.inspiration_sources:
                if source.analysis and "error" not in source.analysis:
                    analyses.append(source.analysis)

            # Generate design system
            if analyses:
                design_system_dict = await design_system_generator.create_design_system(
                    analyses, user_preferences=state.user_request
                )
            else:
                # Fallback design system
                design_system_dict = await self._create_fallback_design_system(state)

            if "error" not in design_system_dict:
                # Convert to DesignSystem object
                state.design_system = DesignSystem(
                    theme=design_system_dict.get("theme", "Modern Minimal"),
                    color_palette=design_system_dict.get("colorPalette", {}),
                    typography=design_system_dict.get("typography", {}),
                    layout=design_system_dict.get("layout", {})
                )
                state.set_phase("await_design_approval")
            else:
                # Error in generation, create fallback
                state.design_system = await self._create_fallback_design_system_object()
                state.set_phase("await_design_approval")

        except Exception as e:
            print(f"Error in create_design_system: {e}")
            state.design_system = await self._create_fallback_design_system_object()
            state.set_phase("await_design_approval")

        return state

    async def _generate_component_code(self, state: AgentState) -> AgentState:
        """
        Pilar 3: Generate component code based on the approved design system.
        """
        try:
            if not state.design_system:
                state.set_phase("error")
                return state

            code_generation_prompt = f"""
            Generate React component code for a {state.component_type} based on this design system:

            DESIGN SYSTEM:
            {json.dumps(state.design_system.model_dump(), indent=2)}

            USER REQUEST:
            {state.user_request}

            Requirements:
            1. Use the exact colors, fonts, and layout values from the design system
            2. Create a modern, responsive component
            3. Include Tailwind CSS classes
            4. Make it production-ready with proper structure
            5. Add comments explaining key design decisions

            Return only the JSX component code with proper imports.
            """

            response = self.model.generate_content(code_generation_prompt)
            state.component_code = response.text
            state.set_phase("await_feedback")

        except Exception as e:
            print(f"Error in generate_component_code: {e}")
            state.component_code = f"// Error generating code: {str(e)}"
            state.set_phase("await_feedback")

        return state

    async def _await_design_approval(self, state: AgentState) -> AgentState:
        """
        Wait for user approval of the design system.
        """
        # This phase is handled by the server when user provides approval
        return state

    async def _await_feedback(self, state: AgentState) -> AgentState:
        """
        Handle feedback and iteration phase.
        """
        # This phase is handled by the server when user provides feedback
        return state

    async def process_feedback(self, state: AgentState, feedback: str) -> AgentState:
        """
        Process user feedback and determine next steps.
        """
        try:
            state.add_feedback(feedback)

            # Analyze feedback to determine action
            feedback_prompt = f"""
            Analyze this user feedback and determine what action to take:

            FEEDBACK: {feedback}
            CURRENT PHASE: {state.current_phase}

            Respond with one of these actions:
            - "approve": User is satisfied, mark as complete
            - "iterate_code": User wants changes to the code, regenerate component
            - "iterate_design": User wants changes to the design system
            - "new_inspiration": User wants completely different inspiration

            Respond with just the action word.
            """

            response = self.model.generate_content(feedback_prompt)
            action = response.text.strip().lower()

            if "approve" in action:
                state.is_complete = True
                state.set_phase("complete")
            elif "iterate_code" in action:
                state.next_iteration()
                state.set_phase("generate_component_code")
            elif "iterate_design" in action:
                state.next_iteration()
                state.design_system_approved = False
                state.set_phase("create_design_system")
            elif "new_inspiration" in action:
                state.next_iteration()
                state.inspiration_complete = False
                state.inspiration_sources = []
                state.set_phase("research_inspiration")
            else:
                # Default to code iteration
                state.next_iteration()
                state.set_phase("generate_component_code")

        except Exception as e:
            print(f"Error processing feedback: {e}")
            state.set_phase("generate_component_code")

        return state

    async def _create_fallback_design_system(self, state=None) -> Dict[str, Any]:
        """Create a fallback design system when inspiration analysis fails."""
        return {
            "theme": "Modern Minimal",
            "colorPalette": {
                "primary": "#2563eb",
                "secondary": "#64748b",
                "accent": "#f59e0b",
                "background": "#ffffff",
                "text": "#1f2937"
            },
            "typography": {
                "headerFont": "Inter",
                "bodyFont": "Inter",
                "baseFontSize": "16px"
            },
            "layout": {
                "gridUnit": 8,
                "borderRadius": "8px",
                "shadows": "0 4px 6px -1px rgba(0, 0, 0, 0.1)"
            }
        }

    async def _create_fallback_design_system_object(self) -> DesignSystem:
        """Create a fallback DesignSystem object."""
        fallback_dict = await self._create_fallback_design_system(None)
        return DesignSystem(
            theme=fallback_dict["theme"],
            color_palette=fallback_dict["colorPalette"],
            typography=fallback_dict["typography"],
            layout=fallback_dict["layout"]
        )


# Global agent instance
ui_design_agent = UIDesignAgent()
