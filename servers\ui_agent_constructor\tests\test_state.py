"""
Unit tests for the state management module.
"""

import pytest
from datetime import datetime
import sys
from pathlib import Path

# Add parent directory to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent))

from state import AgentState, DesignSystem, SessionManager, InspirationSource, FeedbackEntry


class TestDesignSystem:
    """Test the DesignSystem model."""
    
    def test_design_system_creation(self):
        """Test creating a design system with valid data."""
        ds = DesignSystem(
            theme="Modern Dark",
            color_palette={
                "primary": "#2563eb",
                "secondary": "#64748b",
                "accent": "#f59e0b",
                "background": "#0f172a",
                "text": "#f8fafc"
            },
            typography={
                "headerFont": "Inter",
                "bodyFont": "Inter",
                "baseFontSize": "16px"
            },
            layout={
                "gridUnit": 8,
                "borderRadius": "12px",
                "shadows": "0 8px 32px rgba(0, 0, 0, 0.3)"
            }
        )
        
        assert ds.theme == "Modern Dark"
        assert ds.color_palette["primary"] == "#2563eb"
        assert ds.typography["headerFont"] == "Inter"
        assert ds.layout["gridUnit"] == 8
    
    def test_design_system_model_dump(self):
        """Test that model_dump works correctly."""
        ds = DesignSystem(
            theme="Test Theme",
            color_palette={"primary": "#000000"},
            typography={"headerFont": "Arial"},
            layout={"gridUnit": 4}
        )
        
        dumped = ds.model_dump()
        assert isinstance(dumped, dict)
        assert dumped["theme"] == "Test Theme"
        assert dumped["color_palette"]["primary"] == "#000000"


class TestInspirationSource:
    """Test the InspirationSource model."""
    
    def test_inspiration_source_creation(self):
        """Test creating an inspiration source."""
        source = InspirationSource(
            url="https://example.com",
            title="Example Design",
            description="A beautiful design example"
        )
        
        assert source.url == "https://example.com"
        assert source.title == "Example Design"
        assert source.description == "A beautiful design example"
        assert isinstance(source.timestamp, datetime)
    
    def test_inspiration_source_with_analysis(self):
        """Test inspiration source with analysis data."""
        analysis = {
            "colors": ["#ff0000", "#00ff00"],
            "style": "modern"
        }
        
        source = InspirationSource(
            url="https://example.com",
            analysis=analysis
        )
        
        assert source.analysis == analysis
        assert source.analysis["colors"] == ["#ff0000", "#00ff00"]


class TestFeedbackEntry:
    """Test the FeedbackEntry model."""
    
    def test_feedback_entry_creation(self):
        """Test creating a feedback entry."""
        feedback = FeedbackEntry(
            feedback_text="Make it more colorful",
            iteration_number=1
        )
        
        assert feedback.feedback_text == "Make it more colorful"
        assert feedback.iteration_number == 1
        assert isinstance(feedback.timestamp, datetime)


class TestAgentState:
    """Test the AgentState model."""
    
    def test_agent_state_creation(self):
        """Test creating an agent state."""
        state = AgentState(
            user_request="Create a button",
            session_id="test-123"
        )
        
        assert state.user_request == "Create a button"
        assert state.session_id == "test-123"
        assert state.current_phase == "analyze_request"
        assert state.current_iteration == 1
        assert not state.is_complete
        assert len(state.inspiration_sources) == 0
        assert len(state.feedback_history) == 0
    
    def test_agent_state_phase_transitions(self):
        """Test phase transitions."""
        state = AgentState(
            user_request="Create a button",
            session_id="test-123"
        )
        
        original_time = state.updated_at
        
        # Test phase change
        state.set_phase("research_inspiration")
        assert state.current_phase == "research_inspiration"
        assert state.updated_at > original_time
    
    def test_agent_state_feedback_handling(self):
        """Test feedback handling."""
        state = AgentState(
            user_request="Create a button",
            session_id="test-123"
        )
        
        # Add feedback
        state.add_feedback("Make it bigger")
        assert len(state.feedback_history) == 1
        assert state.feedback_history[0].feedback_text == "Make it bigger"
        assert state.feedback_history[0].iteration_number == 1
    
    def test_agent_state_iteration_handling(self):
        """Test iteration handling."""
        state = AgentState(
            user_request="Create a button",
            session_id="test-123"
        )
        
        original_iteration = state.current_iteration
        state.next_iteration()
        
        assert state.current_iteration == original_iteration + 1
    
    def test_agent_state_with_design_system(self):
        """Test agent state with design system."""
        ds = DesignSystem(
            theme="Test Theme",
            color_palette={"primary": "#000000"},
            typography={"headerFont": "Arial"},
            layout={"gridUnit": 4}
        )
        
        state = AgentState(
            user_request="Create a button",
            session_id="test-123",
            design_system=ds
        )
        
        assert state.design_system is not None
        assert state.design_system.theme == "Test Theme"


class TestSessionManager:
    """Test the SessionManager class."""
    
    def setup_method(self):
        """Setup for each test method."""
        self.session_manager = SessionManager()
    
    def test_create_session(self):
        """Test creating a new session."""
        session_id = "test-session-001"
        user_request = "Create a modern button"
        
        state = self.session_manager.create_session(session_id, user_request)
        
        assert state.session_id == session_id
        assert state.user_request == user_request
        assert session_id in self.session_manager.sessions
    
    def test_get_existing_session(self):
        """Test retrieving an existing session."""
        session_id = "test-session-002"
        user_request = "Create a card component"
        
        # Create session
        original_state = self.session_manager.create_session(session_id, user_request)
        
        # Retrieve session
        retrieved_state = self.session_manager.get_session(session_id)
        
        assert retrieved_state is not None
        assert retrieved_state.session_id == original_state.session_id
        assert retrieved_state.user_request == original_state.user_request
    
    def test_get_nonexistent_session(self):
        """Test retrieving a non-existent session."""
        result = self.session_manager.get_session("nonexistent-session")
        assert result is None
    
    def test_update_session(self):
        """Test updating an existing session."""
        session_id = "test-session-003"
        user_request = "Create a form"
        
        # Create session
        state = self.session_manager.create_session(session_id, user_request)
        original_time = state.updated_at
        
        # Modify state
        state.set_phase("research_inspiration")
        
        # Update session
        self.session_manager.update_session(session_id, state)
        
        # Retrieve and verify
        updated_state = self.session_manager.get_session(session_id)
        assert updated_state.current_phase == "research_inspiration"
        assert updated_state.updated_at > original_time
    
    def test_delete_session(self):
        """Test deleting a session."""
        session_id = "test-session-004"
        user_request = "Create a navbar"
        
        # Create session
        self.session_manager.create_session(session_id, user_request)
        assert session_id in self.session_manager.sessions
        
        # Delete session
        self.session_manager.delete_session(session_id)
        assert session_id not in self.session_manager.sessions
        
        # Verify it's gone
        result = self.session_manager.get_session(session_id)
        assert result is None
    
    def test_list_sessions(self):
        """Test listing all sessions."""
        # Start with empty manager
        assert len(self.session_manager.list_sessions()) == 0
        
        # Create multiple sessions
        session_ids = ["session-1", "session-2", "session-3"]
        for session_id in session_ids:
            self.session_manager.create_session(session_id, f"Request for {session_id}")
        
        # List sessions
        listed_sessions = self.session_manager.list_sessions()
        assert len(listed_sessions) == 3
        
        for session_id in session_ids:
            assert session_id in listed_sessions
    
    def test_session_isolation(self):
        """Test that sessions are properly isolated."""
        # Create two sessions
        state1 = self.session_manager.create_session("session-1", "Request 1")
        state2 = self.session_manager.create_session("session-2", "Request 2")
        
        # Modify one session
        state1.set_phase("research_inspiration")
        state1.add_feedback("Feedback for session 1")
        self.session_manager.update_session("session-1", state1)
        
        # Verify the other session is unchanged
        retrieved_state2 = self.session_manager.get_session("session-2")
        assert retrieved_state2.current_phase == "analyze_request"
        assert len(retrieved_state2.feedback_history) == 0


if __name__ == "__main__":
    pytest.main([__file__])
