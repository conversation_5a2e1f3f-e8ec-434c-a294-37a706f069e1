"""
Ejemplo: Crear un Dashboard de Analytics
Este ejemplo muestra cómo crear un dashboard moderno con el UI Agent Constructor.
"""

import asyncio
import json
from pathlib import Path
import sys

# Agregar el directorio padre al path
sys.path.append(str(Path(__file__).parent.parent))

from state import session_manager
from graph import ui_design_agent


async def create_analytics_dashboard():
    """
    Ejemplo de creación de un dashboard de analytics.
    """
    print("📊 Ejemplo: Creando Dashboard de Analytics")
    print("=" * 50)
    
    session_id = "analytics-dashboard-example"
    user_request = """
    Crea un dashboard de analytics moderno para una plataforma de e-commerce.
    
    Características requeridas:
    - Layout tipo Bento Grid con cards asimétricas
    - Tema claro con acentos verdes para métricas positivas
    - Secciones: KPIs principales, gráficos de ventas, tabla de productos top
    - Estilo minimalista y profesional
    - Responsive design
    - Componentes interactivos (hover states, tooltips)
    """
    
    print(f"📝 Creando dashboard para e-commerce...")
    
    # Crear y procesar sesión
    state = session_manager.create_session(session_id, user_request)
    
    try:
        # Procesar a través de todas las fases
        phases_completed = []
        max_iterations = 6
        
        for i in range(max_iterations):
            current_phase = state.current_phase
            phases_completed.append(current_phase)
            
            print(f"   🔄 Fase {i+1}: {current_phase}")
            
            if current_phase in ["await_design_approval", "await_feedback", "complete", "error"]:
                break
                
            state = await ui_design_agent.process_request(state)
            session_manager.update_session(session_id, state)
        
        # Simular aprobación del sistema de diseño si es necesario
        if state.current_phase == "await_design_approval" and state.design_system:
            print("   👍 Simulando aprobación del sistema de diseño...")
            state.design_system_approved = True
            state.set_phase("generate_component_code")
            
            state = await ui_design_agent.process_request(state)
            session_manager.update_session(session_id, state)
        
        # Mostrar resultados
        print(f"\n📊 Dashboard creado - Estado: {state.current_phase}")
        
        if state.design_system:
            print(f"\n🎨 Sistema de Diseño:")
            print(f"   Tema: {state.design_system.theme}")
            colors = state.design_system.color_palette
            print(f"   Paleta: Primary({colors.get('primary', 'N/A')}), Accent({colors.get('accent', 'N/A')})")
        
        return state
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return None


async def create_mobile_app_ui():
    """
    Ejemplo adicional: UI para aplicación móvil.
    """
    print("\n📱 Ejemplo: Creando UI de App Móvil")
    print("=" * 40)
    
    session_id = "mobile-app-example"
    user_request = """
    Diseña la pantalla principal de una app de fitness.
    
    Características:
    - Diseño mobile-first
    - Cards para estadísticas diarias (pasos, calorías, ejercicios)
    - Botón CTA prominente para "Iniciar Workout"
    - Tema oscuro con acentos naranjas/rojos energéticos
    - Navegación bottom tab
    - Micro-interacciones suaves
    """
    
    state = session_manager.create_session(session_id, user_request)
    
    try:
        # Procesamiento rápido
        for i in range(4):
            if state.current_phase in ["await_design_approval", "await_feedback", "complete", "error"]:
                break
            print(f"   📱 Procesando fase: {state.current_phase}")
            state = await ui_design_agent.process_request(state)
            session_manager.update_session(session_id, state)
        
        print(f"   ✅ App UI creada - Estado: {state.current_phase}")
        return state
        
    except Exception as e:
        print(f"   ❌ Error en app móvil: {e}")
        return None


def compare_design_systems(dashboard_state, mobile_state):
    """
    Compara los sistemas de diseño generados para diferentes tipos de UI.
    """
    print("\n🔍 Comparación de Sistemas de Diseño")
    print("=" * 40)
    
    if not dashboard_state or not mobile_state:
        print("❌ No se pueden comparar - falta información")
        return
    
    dashboard_ds = dashboard_state.design_system
    mobile_ds = mobile_state.design_system
    
    if not dashboard_ds or not mobile_ds:
        print("❌ Sistemas de diseño no disponibles")
        return
    
    print("📊 Dashboard vs 📱 Mobile App:")
    print(f"   Temas: '{dashboard_ds.theme}' vs '{mobile_ds.theme}'")
    
    # Comparar colores primarios
    dash_primary = dashboard_ds.color_palette.get('primary', 'N/A')
    mobile_primary = mobile_ds.color_palette.get('primary', 'N/A')
    print(f"   Colores primarios: {dash_primary} vs {mobile_primary}")
    
    # Comparar tipografías
    dash_font = dashboard_ds.typography.get('headerFont', 'N/A')
    mobile_font = mobile_ds.typography.get('headerFont', 'N/A')
    print(f"   Tipografías: {dash_font} vs {mobile_font}")
    
    # Análisis de diferencias
    print("\n🎯 Análisis:")
    if dashboard_ds.theme != mobile_ds.theme:
        print("   ✅ El agente adaptó el tema según el contexto")
    if dash_primary != mobile_primary:
        print("   ✅ Paletas de colores diferenciadas por uso")
    
    print("   📈 El agente demuestra adaptabilidad contextual")


async def batch_ui_generation():
    """
    Demuestra la generación de múltiples UIs en lote.
    """
    print("\n🚀 Generación en Lote de UIs")
    print("=" * 30)
    
    ui_requests = [
        ("login-form", "Formulario de login minimalista con autenticación social"),
        ("pricing-table", "Tabla de precios para SaaS con 3 tiers"),
        ("user-profile", "Página de perfil de usuario con edición inline")
    ]
    
    results = {}
    
    for ui_id, request in ui_requests:
        print(f"   🎨 Generando: {ui_id}")
        
        try:
            state = session_manager.create_session(ui_id, request)
            
            # Procesamiento rápido (solo análisis inicial)
            state = await ui_design_agent.process_request(state)
            session_manager.update_session(ui_id, state)
            
            results[ui_id] = {
                "status": state.current_phase,
                "query": state.inspiration_query,
                "theme": state.design_system.theme if state.design_system else "N/A"
            }
            
            print(f"      ✅ {ui_id}: {state.current_phase}")
            
        except Exception as e:
            print(f"      ❌ {ui_id}: Error - {e}")
            results[ui_id] = {"status": "error", "error": str(e)}
    
    print(f"\n📊 Resultados del lote: {len(results)} UIs procesadas")
    return results


async def main():
    """
    Ejecuta todos los ejemplos de dashboard.
    """
    print("🎯 UI Agent Constructor - Ejemplos de Dashboard")
    print("Demostrando versatilidad del agente con diferentes tipos de UI")
    print()
    
    # Ejemplo 1: Dashboard de analytics
    dashboard_state = await create_analytics_dashboard()
    
    # Ejemplo 2: App móvil
    mobile_state = await create_mobile_app_ui()
    
    # Comparación
    compare_design_systems(dashboard_state, mobile_state)
    
    # Generación en lote
    batch_results = await batch_ui_generation()
    
    # Resumen final
    print("\n" + "=" * 50)
    print("🎉 Ejemplos de Dashboard Completados")
    print(f"   📊 Dashboard: {'✅' if dashboard_state else '❌'}")
    print(f"   📱 Mobile App: {'✅' if mobile_state else '❌'}")
    print(f"   🚀 Lote: {len(batch_results)} UIs")
    
    print("\n💡 Observaciones:")
    print("   • El agente adapta el estilo según el contexto")
    print("   • Diferentes tipos de UI generan diferentes sistemas de diseño")
    print("   • El procesamiento en lote es eficiente para múltiples UIs")
    
    print("\n🔧 Para mejores resultados:")
    print("   • Configura API keys reales")
    print("   • Proporciona contexto específico en las solicitudes")
    print("   • Usa el feedback loop para refinar los diseños")


if __name__ == "__main__":
    asyncio.run(main())
