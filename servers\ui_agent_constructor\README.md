# UI Agent Constructor - MCP Server

An advanced MCP server that provides a UI design agent following the **Three Pillars** approach: Inspiration → Systematization → Composition.

## Features

- 🎨 **Intelligent Design Inspiration**: Searches the web for design inspiration from top design sites
- 🔍 **AI-Powered Image Analysis**: Uses Google Gemini Vision to analyze design patterns, colors, and layouts
- 🎯 **Systematic Design Systems**: Generates consistent design systems in JSON format
- 💻 **Premium Component Generation**: Creates React components using modern UI libraries
- 🔄 **Iterative Feedback Loop**: Supports continuous refinement based on user feedback

## Architecture

The agent follows a structured workflow:

1. **Analyze Request**: Understands user requirements and determines inspiration needs
2. **Research Inspiration**: Searches web and analyzes design examples
3. **Create Design System**: Synthesizes inspiration into a cohesive design system
4. **Generate Component Code**: Creates React/JSX code based on the design system
5. **Await Feedback**: Iterates based on user feedback until satisfaction

## Prerequisites

- Python 3.11+
- Google AI API Key (for Gemini Pro)
- Tavily API Key (for web search, optional)
- Docker (for containerized deployment)

## Setup

### 1. Environment Configuration

Copy the example environment file and configure your API keys:

```bash
cp .env.example .env
```

Edit `.env` with your API keys:

```env
GOOGLE_API_KEY=your_google_ai_api_key_here
TAVILY_API_KEY=your_tavily_api_key_here
```

### 2. Local Development

Install dependencies:

```bash
pip install -r requirements.txt
```

Run the server:

```bash
python server.py
```

### 3. Docker Deployment

Build and run with Docker Compose:

```bash
docker-compose up --build
```

## Usage

The server exposes the following MCP tools:

### `create_ui_design`

Create a new UI design following the Three Pillars approach.

**Parameters:**
- `request` (string, required): Description of the UI component to design
- `session_id` (string, optional): Session ID for continuing a design

**Example:**
```json
{
  "request": "Create a modern SaaS landing page with dark theme and glassmorphism effects"
}
```

### `provide_feedback`

Provide feedback on a generated design to iterate and improve it.

**Parameters:**
- `feedback` (string, required): Your feedback on the current design
- `session_id` (string, required): Session ID of the design

**Example:**
```json
{
  "feedback": "Make the colors more vibrant and add more spacing between elements",
  "session_id": "abc-123-def"
}
```

### `get_design_status`

Get the current status and progress of a design session.

**Parameters:**
- `session_id` (string, required): Session ID to check

### `list_active_sessions`

List all active design sessions.

## Design System Format

The agent generates design systems in this standardized format:

```json
{
  "theme": "Dark Minimalist",
  "colorPalette": {
    "primary": "#2563eb",
    "secondary": "#64748b",
    "accent": "#f59e0b",
    "background": "#ffffff",
    "text": "#1f2937"
  },
  "typography": {
    "headerFont": "Inter",
    "bodyFont": "Inter", 
    "baseFontSize": "16px"
  },
  "layout": {
    "gridUnit": 8,
    "borderRadius": "8px",
    "shadows": "0 4px 6px -1px rgba(0, 0, 0, 0.1)"
  }
}
```

## API Keys Setup

### Google AI API Key

1. Go to [Google AI Studio](https://makersuite.google.com/app/apikey)
2. Create a new API key
3. Add it to your `.env` file as `GOOGLE_API_KEY`

### Tavily API Key (Optional)

1. Sign up at [Tavily](https://tavily.com)
2. Get your API key from the dashboard
3. Add it to your `.env` file as `TAVILY_API_KEY`

Note: If Tavily API key is not provided, the agent will use fallback inspiration sources.

## Development

### Project Structure

```
servers/ui_agent_constructor/
├── server.py          # MCP server implementation
├── graph.py           # Agent workflow logic
├── tools.py           # Agent tools (web search, image analysis)
├── state.py           # State management and data models
├── requirements.txt   # Python dependencies
├── Dockerfile         # Container configuration
├── docker-compose.yml # Docker Compose setup
└── README.md          # This file
```

### Testing

Run tests with pytest:

```bash
pytest
```

## Troubleshooting

### Common Issues

1. **"Google AI API key not configured"**
   - Ensure `GOOGLE_API_KEY` is set in your `.env` file
   - Verify the API key is valid and has Gemini Pro access

2. **"Web inspiration search may be limited"**
   - This is a warning when `TAVILY_API_KEY` is not set
   - The agent will still work with fallback inspiration sources

3. **"Error downloading image"**
   - Some websites may block automated image access
   - The agent will continue with available inspiration sources

### Logs

Enable debug logging by setting `DEBUG=true` in your `.env` file.

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Submit a pull request

## License

This project is part of the awesome-mcp-servers collection and follows the same license terms.
