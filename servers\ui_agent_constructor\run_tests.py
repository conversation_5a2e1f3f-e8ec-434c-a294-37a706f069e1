#!/usr/bin/env python3
"""
Test runner for the UI Agent Constructor.
Provides different test execution modes and reporting.
"""

import sys
import subprocess
import argparse
from pathlib import Path


def run_command(cmd, description):
    """Run a command and return success status."""
    print(f"\n🧪 {description}")
    print("=" * 50)
    
    try:
        result = subprocess.run(cmd, shell=True, check=True, capture_output=True, text=True)
        print(result.stdout)
        if result.stderr:
            print("STDERR:", result.stderr)
        print(f"✅ {description} completed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} failed")
        print("STDOUT:", e.stdout)
        print("STDERR:", e.stderr)
        return False


def run_unit_tests():
    """Run unit tests only."""
    return run_command("pytest tests/ -m 'not integration' -v", "Running Unit Tests")


def run_integration_tests():
    """Run integration tests only."""
    return run_command("pytest tests/ -m integration -v", "Running Integration Tests")


def run_all_tests():
    """Run all tests."""
    return run_command("pytest tests/ -v", "Running All Tests")


def run_coverage_tests():
    """Run tests with coverage reporting."""
    commands = [
        "pip install coverage pytest-cov",
        "pytest tests/ --cov=. --cov-report=html --cov-report=term-missing -v"
    ]
    
    success = True
    for cmd in commands:
        if not run_command(cmd, f"Coverage Test Step: {cmd.split()[0]}"):
            success = False
            break
    
    if success:
        print("\n📊 Coverage report generated in htmlcov/index.html")
    
    return success


def run_quick_tests():
    """Run quick tests (excluding slow ones)."""
    return run_command("pytest tests/ -m 'not slow' -v", "Running Quick Tests")


def run_specific_test(test_file):
    """Run a specific test file."""
    test_path = Path("tests") / test_file
    if not test_path.exists():
        print(f"❌ Test file not found: {test_path}")
        return False
    
    return run_command(f"pytest {test_path} -v", f"Running {test_file}")


def lint_code():
    """Run code linting."""
    commands = [
        "black --check .",
        "flake8 . --max-line-length=100 --ignore=E203,W503"
    ]
    
    success = True
    for cmd in commands:
        if not run_command(cmd, f"Linting: {cmd.split()[0]}"):
            success = False
    
    return success


def format_code():
    """Format code with black."""
    return run_command("black .", "Formatting Code with Black")


def main():
    """Main test runner function."""
    parser = argparse.ArgumentParser(description="UI Agent Constructor Test Runner")
    parser.add_argument(
        "mode",
        choices=["unit", "integration", "all", "coverage", "quick", "lint", "format"],
        nargs="?",
        default="all",
        help="Test mode to run (default: all)"
    )
    parser.add_argument(
        "--file",
        help="Run specific test file (e.g., test_state.py)"
    )
    parser.add_argument(
        "--install-deps",
        action="store_true",
        help="Install test dependencies first"
    )
    
    args = parser.parse_args()
    
    print("🚀 UI Agent Constructor Test Runner")
    print("=" * 50)
    
    # Install dependencies if requested
    if args.install_deps:
        deps_success = run_command(
            "pip install pytest pytest-asyncio pytest-cov black flake8",
            "Installing Test Dependencies"
        )
        if not deps_success:
            print("❌ Failed to install dependencies")
            return 1
    
    # Run specific test file if provided
    if args.file:
        success = run_specific_test(args.file)
        return 0 if success else 1
    
    # Run tests based on mode
    if args.mode == "unit":
        success = run_unit_tests()
    elif args.mode == "integration":
        success = run_integration_tests()
    elif args.mode == "all":
        success = run_all_tests()
    elif args.mode == "coverage":
        success = run_coverage_tests()
    elif args.mode == "quick":
        success = run_quick_tests()
    elif args.mode == "lint":
        success = lint_code()
    elif args.mode == "format":
        success = format_code()
    else:
        print(f"❌ Unknown mode: {args.mode}")
        return 1
    
    # Summary
    print("\n" + "=" * 50)
    if success:
        print("🎉 All tests completed successfully!")
        print("\n💡 Next steps:")
        print("   • Review any test output above")
        print("   • Check coverage report if generated")
        print("   • Run 'python run_tests.py coverage' for detailed coverage")
    else:
        print("❌ Some tests failed. Please review the output above.")
        print("\n🔧 Troubleshooting:")
        print("   • Check that all dependencies are installed")
        print("   • Verify API keys are configured (for integration tests)")
        print("   • Run 'python run_tests.py --install-deps' to install dependencies")
    
    return 0 if success else 1


if __name__ == "__main__":
    sys.exit(main())
