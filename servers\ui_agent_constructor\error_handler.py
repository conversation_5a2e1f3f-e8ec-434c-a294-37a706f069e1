"""
Centralized error handling for the UI Design Agent.
Provides consistent error handling, logging, and user-friendly error messages.
"""

import traceback
from typing import Dict, Any, Optional, Union
from enum import Enum
from dataclasses import dataclass
from logger import get_logger

logger = get_logger("error_handler")


class ErrorType(Enum):
    """Types of errors that can occur in the UI Design Agent."""
    
    # Configuration errors
    API_KEY_MISSING = "api_key_missing"
    INVALID_CONFIG = "invalid_config"
    
    # Network/API errors
    NETWORK_ERROR = "network_error"
    API_RATE_LIMIT = "api_rate_limit"
    API_QUOTA_EXCEEDED = "api_quota_exceeded"
    API_INVALID_RESPONSE = "api_invalid_response"
    
    # Processing errors
    INVALID_INPUT = "invalid_input"
    PROCESSING_FAILED = "processing_failed"
    PARSING_ERROR = "parsing_error"
    
    # State management errors
    SESSION_NOT_FOUND = "session_not_found"
    INVALID_STATE = "invalid_state"
    
    # Tool errors
    WEB_SEARCH_FAILED = "web_search_failed"
    IMAGE_ANALYSIS_FAILED = "image_analysis_failed"
    DESIGN_GENERATION_FAILED = "design_generation_failed"
    
    # System errors
    INTERNAL_ERROR = "internal_error"
    TIMEOUT = "timeout"


@dataclass
class ErrorInfo:
    """Information about an error that occurred."""
    
    error_type: ErrorType
    message: str
    details: Optional[Dict[str, Any]] = None
    recoverable: bool = True
    user_message: Optional[str] = None
    suggested_action: Optional[str] = None


class UIAgentError(Exception):
    """Base exception for UI Design Agent errors."""
    
    def __init__(self, error_info: ErrorInfo, original_exception: Optional[Exception] = None):
        self.error_info = error_info
        self.original_exception = original_exception
        super().__init__(error_info.message)


class ErrorHandler:
    """Centralized error handler for the UI Design Agent."""
    
    def __init__(self):
        self.error_patterns = self._setup_error_patterns()
    
    def _setup_error_patterns(self) -> Dict[str, ErrorInfo]:
        """Setup common error patterns and their handling."""
        return {
            # API Key errors
            "google ai api key not configured": ErrorInfo(
                error_type=ErrorType.API_KEY_MISSING,
                message="Google AI API key is not configured",
                user_message="Please configure your Google AI API key in the .env file",
                suggested_action="Add GOOGLE_API_KEY=your_key_here to your .env file",
                recoverable=True
            ),
            "tavily api key": ErrorInfo(
                error_type=ErrorType.API_KEY_MISSING,
                message="Tavily API key is not configured",
                user_message="Web search functionality is limited without Tavily API key",
                suggested_action="Add TAVILY_API_KEY=your_key_here to your .env file for enhanced web search",
                recoverable=True
            ),
            
            # Network errors
            "connection error": ErrorInfo(
                error_type=ErrorType.NETWORK_ERROR,
                message="Network connection failed",
                user_message="Unable to connect to external services",
                suggested_action="Check your internet connection and try again",
                recoverable=True
            ),
            "timeout": ErrorInfo(
                error_type=ErrorType.TIMEOUT,
                message="Request timed out",
                user_message="The request took too long to complete",
                suggested_action="Try again with a simpler request or check your connection",
                recoverable=True
            ),
            
            # API errors
            "rate limit": ErrorInfo(
                error_type=ErrorType.API_RATE_LIMIT,
                message="API rate limit exceeded",
                user_message="Too many requests sent to the API",
                suggested_action="Please wait a moment before trying again",
                recoverable=True
            ),
            "quota exceeded": ErrorInfo(
                error_type=ErrorType.API_QUOTA_EXCEEDED,
                message="API quota exceeded",
                user_message="API usage limit has been reached",
                suggested_action="Check your API usage limits or upgrade your plan",
                recoverable=False
            ),
            
            # Processing errors
            "json": ErrorInfo(
                error_type=ErrorType.PARSING_ERROR,
                message="Failed to parse JSON response",
                user_message="Received an unexpected response format",
                suggested_action="Try rephrasing your request or try again",
                recoverable=True
            ),
            "session.*not found": ErrorInfo(
                error_type=ErrorType.SESSION_NOT_FOUND,
                message="Session not found",
                user_message="The design session could not be found",
                suggested_action="Start a new design session",
                recoverable=True
            )
        }
    
    def handle_exception(self, 
                        exception: Exception, 
                        context: Optional[Dict[str, Any]] = None,
                        session_id: Optional[str] = None) -> ErrorInfo:
        """
        Handle an exception and return structured error information.
        
        Args:
            exception: The exception that occurred
            context: Additional context about where the error occurred
            session_id: Session ID if applicable
            
        Returns:
            ErrorInfo object with structured error details
        """
        error_message = str(exception).lower()
        
        # Try to match against known error patterns
        for pattern, error_info in self.error_patterns.items():
            if pattern in error_message:
                # Create a copy with additional context
                enhanced_error = ErrorInfo(
                    error_type=error_info.error_type,
                    message=error_info.message,
                    details={
                        "original_error": str(exception),
                        "context": context,
                        "session_id": session_id,
                        "traceback": traceback.format_exc()
                    },
                    recoverable=error_info.recoverable,
                    user_message=error_info.user_message,
                    suggested_action=error_info.suggested_action
                )
                
                # Log the error
                self._log_error(enhanced_error, exception)
                return enhanced_error
        
        # Unknown error - create generic error info
        generic_error = ErrorInfo(
            error_type=ErrorType.INTERNAL_ERROR,
            message=f"An unexpected error occurred: {str(exception)}",
            details={
                "original_error": str(exception),
                "context": context,
                "session_id": session_id,
                "traceback": traceback.format_exc()
            },
            recoverable=True,
            user_message="An unexpected error occurred while processing your request",
            suggested_action="Please try again or contact support if the problem persists"
        )
        
        self._log_error(generic_error, exception)
        return generic_error
    
    def _log_error(self, error_info: ErrorInfo, original_exception: Exception):
        """Log error information appropriately."""
        log_context = {
            "error_type": error_info.error_type.value,
            "recoverable": error_info.recoverable,
            "details": error_info.details
        }
        
        if error_info.recoverable:
            logger.warning(f"Recoverable error: {error_info.message}", log_context)
        else:
            logger.error(f"Non-recoverable error: {error_info.message}", log_context, exc_info=True)
    
    def format_user_error(self, error_info: ErrorInfo) -> str:
        """
        Format an error for display to the user.
        
        Args:
            error_info: The error information to format
            
        Returns:
            User-friendly error message
        """
        message = f"❌ **Error**: {error_info.user_message or error_info.message}\n\n"
        
        if error_info.suggested_action:
            message += f"💡 **Suggested Action**: {error_info.suggested_action}\n\n"
        
        if error_info.recoverable:
            message += "🔄 This error is recoverable - you can try again.\n"
        else:
            message += "⚠️ This error requires attention before continuing.\n"
        
        # Add error code for debugging
        message += f"\n🔍 **Error Code**: `{error_info.error_type.value}`"
        
        return message
    
    def create_fallback_response(self, error_info: ErrorInfo, operation: str) -> str:
        """
        Create a fallback response when an operation fails.
        
        Args:
            error_info: The error that occurred
            operation: Description of the operation that failed
            
        Returns:
            Fallback response message
        """
        fallback_message = f"## ⚠️ {operation} Encountered an Issue\n\n"
        fallback_message += self.format_user_error(error_info)
        
        # Add operation-specific fallback suggestions
        if "design" in operation.lower():
            fallback_message += "\n\n### 🎨 Alternative Approaches:\n"
            fallback_message += "- Try a simpler design request\n"
            fallback_message += "- Provide more specific requirements\n"
            fallback_message += "- Check that your API keys are configured correctly\n"
        
        elif "inspiration" in operation.lower():
            fallback_message += "\n\n### 🔍 Alternative Approaches:\n"
            fallback_message += "- Use more specific search terms\n"
            fallback_message += "- Try different design keywords\n"
            fallback_message += "- The system can work with manual inspiration if needed\n"
        
        return fallback_message


# Global error handler instance
error_handler = ErrorHandler()


def handle_error(exception: Exception, 
                context: Optional[Dict[str, Any]] = None,
                session_id: Optional[str] = None) -> ErrorInfo:
    """
    Convenience function to handle errors using the global error handler.
    
    Args:
        exception: The exception that occurred
        context: Additional context about where the error occurred
        session_id: Session ID if applicable
        
    Returns:
        ErrorInfo object with structured error details
    """
    return error_handler.handle_exception(exception, context, session_id)


def format_error_for_user(error_info: ErrorInfo) -> str:
    """
    Convenience function to format errors for users.
    
    Args:
        error_info: The error information to format
        
    Returns:
        User-friendly error message
    """
    return error_handler.format_user_error(error_info)


def create_error_response(exception: Exception, 
                         operation: str,
                         context: Optional[Dict[str, Any]] = None,
                         session_id: Optional[str] = None) -> str:
    """
    Create a complete error response for an operation.
    
    Args:
        exception: The exception that occurred
        operation: Description of the operation that failed
        context: Additional context
        session_id: Session ID if applicable
        
    Returns:
        Complete error response message
    """
    error_info = handle_error(exception, context, session_id)
    return error_handler.create_fallback_response(error_info, operation)
