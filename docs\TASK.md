# TASK.md: Checklist de Construcción del Servidor MCP de UI

## Hito 1: Configuración del Entorno

- [ ] **Inicializar el proyecto a partir del fork.**
  - **Contexto**: Partimos de `awesome-mcp-servers` para reutilizar la estructura básica del servidor.
  - **Recurso**: `https://github.com/punkpeye/awesome-mcp-servers`
- [ ] **Crear el directorio `servers/ui_agent_constructor` y sus archivos iniciales.**
  - **Archivos**: `Dockerfile`, `requirements.txt`, `.env`, `server.py`, `graph.py`, `tools.py`, `state.py`.

## Hito 2: Implementación de las Herramientas del Agente (`tools.py`)

- [ ] **Implementar la herramienta `browse_web_for_inspiration`**.
  - **Contexto**: Esta herramienta es crucial para el Pilar 1. Debe poder realizar búsquedas web y devolver una lista de URLs relevantes.
  - **Librería Sugerida**: `tavily-python` (requiere API key) o una integración con una API de búsqueda que elijas.
- [ ] **Implementar la herramienta `analyze_image_content`**.
  - **Contexto**: El núcleo de la fase de inspiración. Esta herramienta recibirá una URL de una imagen y usará un modelo multimodal para describirla.
  - **Recurso/API**: Utiliza la capacidad de visión de Gemini Pro. La función debe enviar la imagen y un prompt como: "Analiza esta imagen de una UI y extrae su paleta de colores, el tipo de layout, la jerarquía tipográfica y el estilo general en formato JSON".
  - **Ejemplo de LangChain**: `https://python.langchain.com/docs/integrations/chat/google_generative_ai/` (ver sección sobre multimodalidad).

## Hito 3: Lógica del Agente y Servidor MCP

- [ ] **Definir el `AgentState` en `state.py`.**
  - **Contexto**: Debe incluir campos para `user_request`, `inspiration_urls`, `analyzed_designs`, `design_system_json`, `component_code`, y `feedback_history`.
- [ ] **Construir el Grafo de LangGraph en `graph.py`.**
  - **Contexto**: Implementa el flujo de nodos descrito en `PLANNING.md`. Asegúrate de que el grafo pueda manejar el ciclo de inspiración -> sistematización -> generación -> feedback.
  - **Referencia Clave**: La lógica debe estar diseñada para seguir los **Tres Pilares** de `RULES.md`.
- [ ] **Implementar el `server.py` para exponer el grafo vía MCP.**
  - **Contexto**: Este script es el puente entre el mundo exterior (IDE) y tu agente. Debe gestionar las sesiones para que cada usuario tenga su propio `AgentState`.
  - **Ejemplo de Referencia**: `https://github.com/punkpeye/awesome-mcp-servers/blob/main/servers/basic/server.py`. Adapta su estructura para invocar tu grafo de LangGraph (`graph.invoke` o `graph.stream`).

## Hito 4: Despliegue y Pruebas

- [ ] **Configurar `Dockerfile` y `docker-compose.yml`.**
  - **Contexto**: Preparar el entorno para que sea fácil de desplegar y ejecutar.
- [ ] **Realizar una prueba de integración completa.**
  - **Escenario de Prueba**: Utiliza el prompt de la sección "Prompt de Inicio para una Tarea de Diseño" para verificar que el agente sigue todos los pasos:
        1. Verifica que invoca la herramienta de `browse_web`.
        2. Confirma que intenta analizar las imágenes.
        3. Valida que genera un `design_system.json` coherente con la inspiración.
        4. Comprueba que el código final refleja el sistema de diseño.
