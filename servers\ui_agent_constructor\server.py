"""
MCP Server for UI Design Agent.
Exposes the UI design agent capabilities through the MCP protocol.
"""

import asyncio
import json
import uuid
import os
from dotenv import load_dotenv

from mcp.server.models import InitializationOptions
from mcp.server import NotificationOptions, Server
from mcp.server.stdio import stdio_server
from mcp.types import (
    Tool,
    TextContent,
    ImageContent,
    EmbeddedResource,
)

from state import session_manager, AgentState
from graph import ui_design_agent
from logger import get_logger, setup_logging, performance_monitor

# Load environment variables
load_dotenv()

# Setup logging
logger = get_logger("server")
setup_logging()

# Initialize MCP server
server = Server("ui_agent_constructor")


@server.list_tools()
async def handle_list_tools() -> list[Tool]:
    """
    List available tools for the UI Design Agent.
    """
    return [
        Tool(
            name="create_ui_design",
            description="Create a UI design following the Three Pillars approach: Inspiration -> Systematization -> Composition",
            inputSchema={
                "type": "object",
                "properties": {
                    "request": {
                        "type": "string",
                        "description": "Description of the UI component or interface to design"
                    },
                    "session_id": {
                        "type": "string",
                        "description": "Optional session ID for continuing a design session"
                    }
                },
                "required": ["request"]
            }
        ),
        Tool(
            name="provide_feedback",
            description="Provide feedback on a generated design to iterate and improve it",
            inputSchema={
                "type": "object",
                "properties": {
                    "feedback": {
                        "type": "string",
                        "description": "Feedback on the current design"
                    },
                    "session_id": {
                        "type": "string",
                        "description": "Session ID of the design to provide feedback on"
                    }
                },
                "required": ["feedback", "session_id"]
            }
        ),
        Tool(
            name="get_design_status",
            description="Get the current status and progress of a design session",
            inputSchema={
                "type": "object",
                "properties": {
                    "session_id": {
                        "type": "string",
                        "description": "Session ID to check status for"
                    }
                },
                "required": ["session_id"]
            }
        ),
        Tool(
            name="list_active_sessions",
            description="List all active design sessions",
            inputSchema={
                "type": "object",
                "properties": {}
            }
        )
    ]


@server.call_tool()
async def handle_call_tool(name: str, arguments: dict) -> list[TextContent | ImageContent | EmbeddedResource]:
    """
    Handle tool calls for the UI Design Agent.
    """

    if name == "create_ui_design":
        return await handle_create_ui_design(arguments)
    elif name == "provide_feedback":
        return await handle_provide_feedback(arguments)
    elif name == "get_design_status":
        return await handle_get_design_status(arguments)
    elif name == "list_active_sessions":
        return await handle_list_active_sessions(arguments)
    else:
        raise ValueError(f"Unknown tool: {name}")


async def handle_create_ui_design(arguments: dict) -> list[TextContent]:
    """
    Handle UI design creation requests.
    """
    request = arguments.get("request", "")
    session_id = arguments.get("session_id", str(uuid.uuid4()))

    logger.log_user_interaction(session_id, "create_ui_design", {
        "request_length": len(request),
        "has_session_id": bool(arguments.get("session_id"))
    })

    if not request:
        logger.warning("Empty request received", {"session_id": session_id})
        return [TextContent(type="text", text="Error: Request description is required")]

    try:
        # Create or get existing session
        state = session_manager.get_session(session_id)
        if not state:
            state = session_manager.create_session(session_id, request)
            logger.info("New session created", {"session_id": session_id})
        else:
            logger.info("Existing session retrieved", {"session_id": session_id})

        # Process the request through the agent
        logger.debug("Processing request through agent", {"session_id": session_id})
        updated_state = await ui_design_agent.process_request(state)
        session_manager.update_session(session_id, updated_state)

        logger.log_agent_phase(session_id, updated_state.current_phase, {
            "iteration": updated_state.current_iteration
        })

        # Format response based on current phase
        response = await format_agent_response(updated_state)

        logger.info("UI design request completed successfully", {
            "session_id": session_id,
            "final_phase": updated_state.current_phase
        })

        return [TextContent(type="text", text=response)]

    except Exception as e:
        error_msg = f"Error processing design request: {str(e)}"
        logger.error("Failed to process UI design request", {
            "session_id": session_id,
            "error": str(e)
        }, exc_info=True)
        return [TextContent(type="text", text=error_msg)]


async def handle_provide_feedback(arguments: dict) -> list[TextContent]:
    """
    Handle feedback on existing designs.
    """
    feedback = arguments.get("feedback", "")
    session_id = arguments.get("session_id", "")

    logger.log_user_interaction(session_id, "provide_feedback", {
        "feedback_length": len(feedback),
        "has_session_id": bool(session_id)
    })

    if not feedback or not session_id:
        logger.warning("Invalid feedback request", {
            "has_feedback": bool(feedback),
            "has_session_id": bool(session_id)
        })
        return [TextContent(type="text", text="Error: Both feedback and session_id are required")]

    try:
        # Get existing session
        state = session_manager.get_session(session_id)
        if not state:
            logger.warning("Session not found for feedback", {"session_id": session_id})
            return [TextContent(type="text", text=f"Error: Session {session_id} not found")]

        logger.info("Processing user feedback", {
            "session_id": session_id,
            "current_phase": state.current_phase,
            "iteration": state.current_iteration
        })

        # Process feedback through the agent
        updated_state = await ui_design_agent.process_feedback(state, feedback)
        session_manager.update_session(session_id, updated_state)

        # Continue processing if needed
        if updated_state.current_phase != "await_feedback" and not updated_state.is_complete:
            logger.debug("Continuing processing after feedback", {"session_id": session_id})
            updated_state = await ui_design_agent.process_request(updated_state)
            session_manager.update_session(session_id, updated_state)

        logger.log_agent_phase(session_id, updated_state.current_phase, {
            "iteration": updated_state.current_iteration,
            "feedback_processed": True
        })

        # Format response
        response = await format_agent_response(updated_state)

        logger.info("Feedback processed successfully", {
            "session_id": session_id,
            "new_phase": updated_state.current_phase
        })

        return [TextContent(type="text", text=response)]

    except Exception as e:
        error_msg = f"Error processing feedback: {str(e)}"
        logger.error("Failed to process feedback", {
            "session_id": session_id,
            "error": str(e)
        }, exc_info=True)
        return [TextContent(type="text", text=error_msg)]


async def handle_get_design_status(arguments: dict) -> list[TextContent]:
    """
    Get the status of a design session.
    """
    session_id = arguments.get("session_id", "")

    if not session_id:
        return [TextContent(type="text", text="Error: session_id is required")]

    try:
        state = session_manager.get_session(session_id)
        if not state:
            return [TextContent(type="text", text=f"Session {session_id} not found")]

        status_info = {
            "session_id": state.session_id,
            "current_phase": state.current_phase,
            "iteration": state.current_iteration,
            "is_complete": state.is_complete,
            "user_request": state.user_request,
            "inspiration_complete": state.inspiration_complete,
            "design_system_approved": state.design_system_approved,
            "created_at": state.created_at.isoformat(),
            "updated_at": state.updated_at.isoformat()
        }

        response = f"## Design Session Status\n\n```json\n{json.dumps(status_info, indent=2)}\n```"

        return [TextContent(type="text", text=response)]

    except Exception as e:
        error_msg = f"Error getting session status: {str(e)}"
        return [TextContent(type="text", text=error_msg)]


async def handle_list_active_sessions(arguments: dict = None) -> list[TextContent]:
    """
    List all active design sessions.
    """
    try:
        session_ids = session_manager.list_sessions()

        if not session_ids:
            return [TextContent(type="text", text="No active design sessions")]

        sessions_info = []
        for session_id in session_ids:
            state = session_manager.get_session(session_id)
            if state:
                sessions_info.append({
                    "session_id": session_id,
                    "phase": state.current_phase,
                    "iteration": state.current_iteration,
                    "complete": state.is_complete,
                    "request": state.user_request[:100] + "..." if len(state.user_request) > 100 else state.user_request
                })

        response = f"## Active Design Sessions ({len(sessions_info)})\n\n"
        for info in sessions_info:
            response += f"**Session:** `{info['session_id']}`\n"
            response += f"- Phase: {info['phase']}\n"
            response += f"- Iteration: {info['iteration']}\n"
            response += f"- Complete: {info['complete']}\n"
            response += f"- Request: {info['request']}\n\n"

        return [TextContent(type="text", text=response)]

    except Exception as e:
        error_msg = f"Error listing sessions: {str(e)}"
        return [TextContent(type="text", text=error_msg)]


async def format_agent_response(state: AgentState) -> str:
    """
    Format the agent's current state into a user-friendly response.
    """
    response = f"# UI Design Agent - Session: `{state.session_id}`\n\n"

    # Show current phase and progress
    response += f"**Current Phase:** {state.current_phase.replace('_', ' ').title()}\n"
    response += f"**Iteration:** {state.current_iteration}\n\n"

    if state.current_phase == "research_inspiration":
        response += "## 🎨 Gathering Design Inspiration\n\n"
        response += f"Searching for: *{state.inspiration_query}*\n\n"

        if state.inspiration_sources:
            response += "### Found Inspiration Sources:\n"
            for i, source in enumerate(state.inspiration_sources, 1):
                response += f"{i}. [{source.title or 'Design Source'}]({source.url})\n"
                if source.description:
                    response += f"   - {source.description}\n"
            response += "\n"

    elif state.current_phase == "create_design_system" or state.current_phase == "await_design_approval":
        response += "## 🎯 Design System Created\n\n"

        if state.design_system:
            response += "### Design System\n\n"
            response += f"**Theme:** {state.design_system.theme}\n\n"

            response += "**Color Palette:**\n"
            for color_name, color_value in state.design_system.color_palette.items():
                response += f"- {color_name.title()}: `{color_value}`\n"
            response += "\n"

            response += "**Typography:**\n"
            for typo_name, typo_value in state.design_system.typography.items():
                response += f"- {typo_name.replace('Font', ' Font').title()}: {typo_value}\n"
            response += "\n"

            response += "**Layout:**\n"
            for layout_name, layout_value in state.design_system.layout.items():
                response += f"- {layout_name.replace('Unit', ' Unit').title()}: {layout_value}\n"
            response += "\n"

            if state.current_phase == "await_design_approval":
                response += "**Please review this design system and provide feedback, or approve it to proceed with code generation.**\n\n"

    elif state.current_phase == "generate_component_code" or state.current_phase == "await_feedback":
        response += "## 💻 Generated Component Code\n\n"

        if state.design_system:
            response += f"**Based on Design System:** {state.design_system.theme}\n\n"

        if state.component_code:
            response += "### Component Code:\n\n"
            response += f"```jsx\n{state.component_code}\n```\n\n"

            if state.current_phase == "await_feedback":
                response += "**Please review the generated code and provide feedback for improvements, or approve if satisfied.**\n\n"

    elif state.current_phase == "complete":
        response += "## ✅ Design Complete!\n\n"
        response += "Your UI design has been completed successfully. The design system and component code are ready for implementation.\n\n"

    elif state.current_phase == "error":
        response += "## ❌ Error\n\n"
        response += "An error occurred during the design process. Please try again or contact support.\n\n"

    # Add feedback history if available
    if state.feedback_history:
        response += "### Feedback History:\n"
        for i, feedback in enumerate(state.feedback_history, 1):
            response += f"{i}. *Iteration {feedback.iteration_number}:* {feedback.feedback_text}\n"
        response += "\n"

    return response


async def main():
    """
    Main entry point for the MCP server.
    """
    # Validate environment variables
    if not os.getenv("GOOGLE_API_KEY"):
        print("Warning: GOOGLE_API_KEY not set. The agent will not function properly.")

    if not os.getenv("TAVILY_API_KEY"):
        print("Warning: TAVILY_API_KEY not set. Web inspiration search may be limited.")

    # Run the MCP server
    async with stdio_server() as (read_stream, write_stream):
        await server.run(
            read_stream,
            write_stream,
            InitializationOptions(
                server_name="ui_agent_constructor",
                server_version="1.0.0",
                capabilities=server.get_capabilities(
                    notification_options=NotificationOptions(),
                    experimental_capabilities={},
                ),
            ),
        )


if __name__ == "__main__":
    asyncio.run(main())
