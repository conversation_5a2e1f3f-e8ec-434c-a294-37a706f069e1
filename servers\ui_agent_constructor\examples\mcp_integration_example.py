"""
Ejemplo: Integración MCP
Este ejemplo muestra cómo interactuar con el servidor MCP directamente.
"""

import asyncio
import json
from pathlib import Path
import sys

# Agregar el directorio padre al path
sys.path.append(str(Path(__file__).parent.parent))

from server import handle_create_ui_design, handle_provide_feedback, handle_get_design_status, handle_list_active_sessions
from mcp.types import TextContent


async def simulate_mcp_client():
    """
    Simula un cliente MCP interactuando con el servidor.
    """
    print("🔌 Simulación de Cliente MCP")
    print("=" * 40)
    
    # Ejemplo 1: Crear un nuevo diseño
    print("1. 🎨 Creando nuevo diseño UI...")
    
    create_args = {
        "request": "Crea un componente de card de producto para e-commerce con imagen, título, precio y botón de compra. Estilo moderno y limpio.",
        "session_id": "mcp-example-session"
    }
    
    try:
        result = await handle_create_ui_design(create_args)
        print("   ✅ Diseño creado exitosamente")
        
        # Mostrar respuesta
        if result and len(result) > 0:
            response_text = result[0].text
            print(f"   📄 Respuesta: {response_text[:200]}...")
        
    except Exception as e:
        print(f"   ❌ Error creando diseño: {e}")
        return
    
    # Ejemplo 2: Verificar estado
    print("\n2. 📊 Verificando estado de la sesión...")
    
    status_args = {"session_id": "mcp-example-session"}
    
    try:
        status_result = await handle_get_design_status(status_args)
        print("   ✅ Estado obtenido")
        
        if status_result and len(status_result) > 0:
            status_text = status_result[0].text
            print(f"   📊 Estado: {status_text[:150]}...")
        
    except Exception as e:
        print(f"   ❌ Error obteniendo estado: {e}")
    
    # Ejemplo 3: Proporcionar feedback
    print("\n3. 💬 Proporcionando feedback...")
    
    feedback_args = {
        "session_id": "mcp-example-session",
        "feedback": "Hazlo más colorido y agrega sombras más pronunciadas para darle más profundidad"
    }
    
    try:
        feedback_result = await handle_provide_feedback(feedback_args)
        print("   ✅ Feedback procesado")
        
        if feedback_result and len(feedback_result) > 0:
            feedback_text = feedback_result[0].text
            print(f"   🔄 Resultado: {feedback_text[:200]}...")
        
    except Exception as e:
        print(f"   ❌ Error procesando feedback: {e}")
    
    # Ejemplo 4: Listar sesiones activas
    print("\n4. 📋 Listando sesiones activas...")
    
    try:
        sessions_result = await handle_list_active_sessions({})
        print("   ✅ Sesiones listadas")
        
        if sessions_result and len(sessions_result) > 0:
            sessions_text = sessions_result[0].text
            print(f"   📋 Sesiones: {sessions_text[:300]}...")
        
    except Exception as e:
        print(f"   ❌ Error listando sesiones: {e}")


def create_mcp_client_config():
    """
    Crea un archivo de configuración para clientes MCP.
    """
    print("\n⚙️ Generando configuración MCP...")
    
    config = {
        "mcpServers": {
            "ui-agent-constructor": {
                "command": "python",
                "args": ["server.py"],
                "cwd": str(Path(__file__).parent.parent),
                "env": {
                    "GOOGLE_API_KEY": "${GOOGLE_API_KEY}",
                    "TAVILY_API_KEY": "${TAVILY_API_KEY}",
                    "DEBUG": "false"
                }
            }
        }
    }
    
    config_path = Path(__file__).parent / "mcp_client_config.json"
    with open(config_path, 'w') as f:
        json.dump(config, f, indent=2)
    
    print(f"   ✅ Configuración guardada en: {config_path}")
    
    # También crear configuración para Claude Desktop
    claude_config = {
        "mcpServers": {
            "ui-agent-constructor": {
                "command": "python",
                "args": [str(Path(__file__).parent.parent / "server.py")],
                "env": {
                    "GOOGLE_API_KEY": "your_google_api_key_here",
                    "TAVILY_API_KEY": "your_tavily_api_key_here"
                }
            }
        }
    }
    
    claude_config_path = Path(__file__).parent / "claude_desktop_config.json"
    with open(claude_config_path, 'w') as f:
        json.dump(claude_config, f, indent=2)
    
    print(f"   ✅ Configuración Claude Desktop: {claude_config_path}")


def demonstrate_mcp_tools():
    """
    Demuestra las herramientas MCP disponibles.
    """
    print("\n🛠️ Herramientas MCP Disponibles")
    print("=" * 35)
    
    tools = [
        {
            "name": "create_ui_design",
            "description": "Crear un nuevo diseño UI siguiendo el enfoque de Tres Pilares",
            "parameters": ["request (requerido)", "session_id (opcional)"],
            "example": {
                "request": "Crea un botón CTA moderno con gradiente",
                "session_id": "mi-sesion-123"
            }
        },
        {
            "name": "provide_feedback",
            "description": "Proporcionar feedback para iterar un diseño existente",
            "parameters": ["feedback (requerido)", "session_id (requerido)"],
            "example": {
                "feedback": "Hazlo más grande y cambia el color a azul",
                "session_id": "mi-sesion-123"
            }
        },
        {
            "name": "get_design_status",
            "description": "Obtener el estado actual de una sesión de diseño",
            "parameters": ["session_id (requerido)"],
            "example": {
                "session_id": "mi-sesion-123"
            }
        },
        {
            "name": "list_active_sessions",
            "description": "Listar todas las sesiones de diseño activas",
            "parameters": [],
            "example": {}
        }
    ]
    
    for i, tool in enumerate(tools, 1):
        print(f"{i}. 🔧 {tool['name']}")
        print(f"   📝 {tool['description']}")
        print(f"   📋 Parámetros: {', '.join(tool['parameters'])}")
        print(f"   💡 Ejemplo: {json.dumps(tool['example'], indent=6)}")
        print()


async def workflow_demonstration():
    """
    Demuestra un flujo de trabajo completo usando MCP.
    """
    print("\n🔄 Demostración de Flujo de Trabajo MCP")
    print("=" * 45)
    
    workflow_steps = [
        "1. Cliente solicita nuevo diseño via MCP",
        "2. Servidor procesa solicitud y busca inspiración",
        "3. Servidor genera sistema de diseño",
        "4. Cliente revisa y proporciona feedback",
        "5. Servidor itera basado en feedback",
        "6. Proceso se repite hasta aprobación final"
    ]
    
    print("📋 Pasos del flujo de trabajo:")
    for step in workflow_steps:
        print(f"   {step}")
    
    print("\n🎯 Ventajas del enfoque MCP:")
    advantages = [
        "✅ Integración nativa con IDEs",
        "✅ Protocolo estándar y bien definido",
        "✅ Sesiones persistentes durante el desarrollo",
        "✅ Feedback loop interactivo",
        "✅ Escalabilidad para múltiples proyectos"
    ]
    
    for advantage in advantages:
        print(f"   {advantage}")


async def main():
    """
    Ejecuta todos los ejemplos de integración MCP.
    """
    print("🔌 UI Agent Constructor - Integración MCP")
    print("Demostrando cómo usar el agente a través del protocolo MCP")
    print()
    
    # Simular cliente MCP
    await simulate_mcp_client()
    
    # Crear configuraciones
    create_mcp_client_config()
    
    # Demostrar herramientas
    demonstrate_mcp_tools()
    
    # Mostrar flujo de trabajo
    await workflow_demonstration()
    
    print("\n" + "=" * 50)
    print("🎉 Ejemplos de Integración MCP Completados")
    print()
    print("📚 Próximos pasos:")
    print("   1. Configura tu IDE con el archivo de configuración generado")
    print("   2. Instala las dependencias: pip install -r requirements.txt")
    print("   3. Configura tus API keys en .env")
    print("   4. Ejecuta el servidor: python server.py")
    print("   5. ¡Comienza a crear UIs increíbles!")
    print()
    print("💡 Tip: Usa descripciones detalladas para mejores resultados")
    print("🔧 Debug: Activa DEBUG=true en .env para más información")


if __name__ == "__main__":
    asyncio.run(main())
