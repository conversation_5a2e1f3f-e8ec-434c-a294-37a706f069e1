"""
Tools for the UI Design Agent.
Implements the core capabilities needed for inspiration gathering and image analysis.
"""

import os
import json
from typing import List, Dict, Any, Optional
import aiohttp
from PIL import Image
import io

import google.generativeai as genai
from tavily import TavilyClient

from state import InspirationSource
from logger import get_logger

# Setup logging for tools module
logger = get_logger("tools")


class WebInspirationTool:
    """Tool for browsing the web to find design inspiration."""

    def __init__(self):
        self.tavily_client = None
        if os.getenv("TAVILY_API_KEY"):
            self.tavily_client = TavilyClient(api_key=os.getenv("TAVILY_API_KEY"))

    async def browse_web_for_inspiration(self, query: str, max_results: int = 5) -> List[InspirationSource]:
        """
        Search the web for design inspiration using the provided query.

        Args:
            query: Search query (e.g., "modern saas landing page", "minimalist dashboard ui")
            max_results: Maximum number of results to return

        Returns:
            List of InspirationSource objects with URLs and metadata
        """
        inspiration_sources = []

        # Enhance query to target design-focused sites
        enhanced_query = f"{query} site:dribbble.com OR site:awwwards.com OR site:siteinspire.com OR site:behance.net"

        try:
            if self.tavily_client:
                # Use Tavily for search
                search_results = self.tavily_client.search(
                    query=enhanced_query,
                    search_depth="basic",
                    max_results=max_results
                )

                for result in search_results.get("results", []):
                    source = InspirationSource(
                        url=result.get("url", ""),
                        title=result.get("title", ""),
                        description=result.get("content", "")[:200] + "..." if result.get("content") else None
                    )
                    inspiration_sources.append(source)
            else:
                # Fallback: Use a simple search approach
                # This is a simplified version - in production, you'd want a more robust search
                fallback_sources = [
                    InspirationSource(
                        url="https://dribbble.com/search/ui-design",
                        title="UI Design Inspiration - Dribbble",
                        description="Collection of UI design inspiration from Dribbble"
                    ),
                    InspirationSource(
                        url="https://www.awwwards.com/websites/",
                        title="Award-winning Website Designs - Awwwards",
                        description="Showcase of award-winning website designs"
                    )
                ]
                inspiration_sources = fallback_sources[:max_results]

        except Exception as e:
            print(f"Error in web search: {e}")
            # Return empty list on error
            pass

        return inspiration_sources


class ImageAnalysisTool:
    """Tool for analyzing images using Google Gemini Vision."""

    def __init__(self):
        # Configure Google AI
        if os.getenv("GOOGLE_API_KEY"):
            genai.configure(api_key=os.getenv("GOOGLE_API_KEY"))
            self.model = genai.GenerativeModel('gemini-1.5-flash')
        else:
            self.model = None

    async def analyze_image_content(self, image_url: str) -> Dict[str, Any]:
        """
        Analyze an image URL using Gemini Vision to extract design elements.

        Args:
            image_url: URL of the image to analyze

        Returns:
            Dictionary containing analysis results with composition, colors, typography, and style
        """
        if not self.model:
            return {"error": "Google AI API key not configured"}

        try:
            # Download the image
            image_data = await self._download_image(image_url)
            if not image_data:
                return {"error": "Failed to download image"}

            # Create the analysis prompt
            analysis_prompt = """
            Analyze this UI/web design image and extract the following information in JSON format:

            {
                "composition": {
                    "layout_type": "string (e.g., 'grid', 'bento', 'asymmetric', 'split-screen')",
                    "visual_hierarchy": "string describing the visual flow",
                    "spacing_style": "string (e.g., 'tight', 'generous', 'minimal')"
                },
                "color_palette": {
                    "primary_color": "hex color code",
                    "secondary_color": "hex color code",
                    "accent_color": "hex color code",
                    "background_color": "hex color code",
                    "text_color": "hex color code"
                },
                "typography": {
                    "header_style": "string describing header typography",
                    "body_style": "string describing body text",
                    "font_weight_usage": "string (e.g., 'bold headers, regular body')"
                },
                "design_style": {
                    "overall_vibe": "string (e.g., 'minimalist', 'glassmorphism', 'brutalist')",
                    "key_characteristics": ["list", "of", "key", "design", "elements"],
                    "interaction_patterns": "string describing UI patterns used"
                }
            }

            Focus on extracting actionable design insights that can be used to create a design system.
            """

            # Generate analysis
            response = self.model.generate_content([analysis_prompt, image_data])

            # Try to parse JSON from response
            try:
                # Extract JSON from the response text
                response_text = response.text
                # Find JSON content (assuming it's wrapped in ```json blocks)
                if "```json" in response_text:
                    json_start = response_text.find("```json") + 7
                    json_end = response_text.find("```", json_start)
                    json_text = response_text[json_start:json_end].strip()
                else:
                    json_text = response_text

                analysis_result = json.loads(json_text)
                return analysis_result

            except json.JSONDecodeError:
                # If JSON parsing fails, return the raw text
                return {
                    "raw_analysis": response.text,
                    "error": "Could not parse JSON response"
                }

        except Exception as e:
            return {"error": f"Analysis failed: {str(e)}"}

    async def _download_image(self, image_url: str) -> Optional[Image.Image]:
        """Download an image from URL and return PIL Image object."""
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(image_url) as response:
                    if response.status == 200:
                        image_bytes = await response.read()
                        image = Image.open(io.BytesIO(image_bytes))
                        return image
        except Exception as e:
            print(f"Error downloading image: {e}")
            return None


class DesignSystemGenerator:
    """Tool for generating design systems from inspiration analysis."""

    def __init__(self):
        if os.getenv("GOOGLE_API_KEY"):
            genai.configure(api_key=os.getenv("GOOGLE_API_KEY"))
            self.model = genai.GenerativeModel('gemini-1.5-flash')
        else:
            self.model = None

    async def create_design_system(self, inspiration_analyses: List[Dict[str, Any]], user_preferences: str = "") -> Dict[str, Any]:
        """
        Create a design system JSON from multiple inspiration analyses.

        Args:
            inspiration_analyses: List of analysis results from images
            user_preferences: Additional user preferences or requirements

        Returns:
            Design system dictionary following the RULES.md format
        """
        if not self.model:
            return {"error": "Google AI API key not configured"}

        try:
            # Create synthesis prompt
            synthesis_prompt = f"""
            Based on the following design analyses and user preferences, create a cohesive design system in the exact JSON format specified:

            INSPIRATION ANALYSES:
            {json.dumps(inspiration_analyses, indent=2)}

            USER PREFERENCES:
            {user_preferences}

            Create a design system that synthesizes the best elements from the inspiration while maintaining consistency.
            Return ONLY the JSON in this exact format:

            {{
                "theme": "string (e.g., 'Dark Minimalist', 'Vibrant Glassmorphism')",
                "colorPalette": {{
                    "primary": "hex color",
                    "secondary": "hex color",
                    "accent": "hex color",
                    "background": "hex color",
                    "text": "hex color"
                }},
                "typography": {{
                    "headerFont": "font name",
                    "bodyFont": "font name",
                    "baseFontSize": "size with unit"
                }},
                "layout": {{
                    "gridUnit": number,
                    "borderRadius": "size with unit",
                    "shadows": "CSS shadow value"
                }}
            }}
            """

            response = self.model.generate_content(synthesis_prompt)

            # Parse JSON response
            try:
                response_text = response.text
                if "```json" in response_text:
                    json_start = response_text.find("```json") + 7
                    json_end = response_text.find("```", json_start)
                    json_text = response_text[json_start:json_end].strip()
                else:
                    json_text = response_text

                design_system = json.loads(json_text)
                return design_system

            except json.JSONDecodeError:
                return {
                    "error": "Could not parse design system JSON",
                    "raw_response": response.text
                }

        except Exception as e:
            return {"error": f"Design system generation failed: {str(e)}"}


# Tool instances
web_inspiration_tool = WebInspirationTool()
image_analysis_tool = ImageAnalysisTool()
design_system_generator = DesignSystemGenerator()
