"""
Ejemplo: Crear una Landing Page para SaaS
Este ejemplo muestra cómo usar el UI Agent Constructor para crear una landing page moderna.
"""

import asyncio
import json
from pathlib import Path
import sys

# Agregar el directorio padre al path para importar los módulos
sys.path.append(str(Path(__file__).parent.parent))

from state import session_manager
from graph import ui_design_agent


async def create_saas_landing_example():
    """
    Ejemplo completo de creación de una landing page para SaaS.
    """
    print("🚀 Ejemplo: Creando Landing Page para SaaS")
    print("=" * 50)
    
    # Paso 1: Crear sesión
    session_id = "saas-landing-example"
    user_request = """
    Crea una landing page moderna para una startup de IA llamada 'NeuralFlow' que ofrece 
    automatización de workflows empresariales. 
    
    Características deseadas:
    - Tema oscuro con acentos azules/púrpuras
    - Estilo moderno y profesional
    - Secciones: Hero, Features, Pricing, CTA
    - Efectos glassmorphism sutiles
    - Optimizada para conversión
    """
    
    print(f"📝 Solicitud: {user_request[:100]}...")
    
    # Crear sesión
    state = session_manager.create_session(session_id, user_request)
    print(f"✅ Sesión creada: {session_id}")
    
    # Paso 2: Procesar a través del agente
    try:
        print("\n🎨 Procesando a través del agente...")
        
        # Análisis inicial
        print("   🔍 Analizando solicitud...")
        state = await ui_design_agent.process_request(state)
        session_manager.update_session(session_id, state)
        print(f"   ✅ Fase actual: {state.current_phase}")
        
        # Continuar procesamiento hasta completar o necesitar aprobación
        max_iterations = 5
        iteration = 0
        
        while (not state.is_complete and 
               state.current_phase not in ["await_design_approval", "await_feedback", "error"] and 
               iteration < max_iterations):
            
            iteration += 1
            print(f"   🔄 Iteración {iteration}: {state.current_phase}")
            
            state = await ui_design_agent.process_request(state)
            session_manager.update_session(session_id, state)
        
        # Mostrar resultados
        print(f"\n📊 Estado final: {state.current_phase}")
        
        if state.design_system:
            print("\n🎯 Sistema de Diseño Generado:")
            print(f"   Tema: {state.design_system.theme}")
            print(f"   Colores: {list(state.design_system.color_palette.keys())}")
            print(f"   Tipografía: {state.design_system.typography.get('headerFont', 'N/A')}")
        
        if state.inspiration_sources:
            print(f"\n🎨 Fuentes de Inspiración ({len(state.inspiration_sources)}):")
            for i, source in enumerate(state.inspiration_sources[:3], 1):
                print(f"   {i}. {source.title or 'Fuente'}: {source.url}")
        
        if state.component_code:
            print(f"\n💻 Código Generado: {len(state.component_code)} caracteres")
            # Mostrar preview del código
            code_preview = state.component_code[:300] + "..." if len(state.component_code) > 300 else state.component_code
            print(f"   Preview: {code_preview}")
        
        return state
        
    except Exception as e:
        print(f"❌ Error durante el procesamiento: {e}")
        return None


async def demonstrate_feedback_loop(state):
    """
    Demuestra cómo usar el loop de feedback para iterar el diseño.
    """
    if not state:
        print("❌ No hay estado válido para demostrar feedback")
        return
    
    print("\n🔄 Demostrando Loop de Feedback")
    print("=" * 30)
    
    # Simular feedback del usuario
    feedback_examples = [
        "Hazlo más colorido y vibrante",
        "Agrega más espaciado entre elementos",
        "Cambia la tipografía a algo más moderno"
    ]
    
    for i, feedback in enumerate(feedback_examples, 1):
        print(f"\n💬 Feedback {i}: {feedback}")
        
        try:
            # Procesar feedback
            updated_state = await ui_design_agent.process_feedback(state, feedback)
            session_manager.update_session(state.session_id, updated_state)
            
            print(f"   ✅ Feedback procesado")
            print(f"   📊 Nueva fase: {updated_state.current_phase}")
            print(f"   🔄 Iteración: {updated_state.current_iteration}")
            
            # Continuar procesamiento si es necesario
            if updated_state.current_phase not in ["await_feedback", "complete", "error"]:
                updated_state = await ui_design_agent.process_request(updated_state)
                session_manager.update_session(state.session_id, updated_state)
                print(f"   🎯 Procesamiento completado: {updated_state.current_phase}")
            
            state = updated_state
            
        except Exception as e:
            print(f"   ❌ Error procesando feedback: {e}")
    
    print(f"\n📈 Resultado final:")
    print(f"   Iteraciones totales: {state.current_iteration}")
    print(f"   Feedback recibido: {len(state.feedback_history)}")
    print(f"   Estado final: {state.current_phase}")


def save_results_to_file(state, filename="saas_landing_results.json"):
    """
    Guarda los resultados del ejemplo en un archivo JSON.
    """
    if not state:
        return
    
    results = {
        "session_id": state.session_id,
        "user_request": state.user_request,
        "final_phase": state.current_phase,
        "iterations": state.current_iteration,
        "design_system": state.design_system.model_dump() if state.design_system else None,
        "inspiration_sources": [
            {
                "title": source.title,
                "url": source.url,
                "description": source.description
            }
            for source in state.inspiration_sources
        ],
        "component_code": state.component_code,
        "feedback_history": [
            {
                "feedback": fb.feedback_text,
                "iteration": fb.iteration_number,
                "timestamp": fb.timestamp.isoformat()
            }
            for fb in state.feedback_history
        ]
    }
    
    filepath = Path(__file__).parent / filename
    with open(filepath, 'w', encoding='utf-8') as f:
        json.dump(results, f, indent=2, ensure_ascii=False)
    
    print(f"\n💾 Resultados guardados en: {filepath}")


async def main():
    """
    Función principal que ejecuta el ejemplo completo.
    """
    print("🎯 UI Agent Constructor - Ejemplo SaaS Landing Page")
    print("Este ejemplo demuestra el flujo completo de creación de UI")
    print()
    
    # Ejecutar ejemplo principal
    state = await create_saas_landing_example()
    
    if state:
        # Demostrar feedback loop
        await demonstrate_feedback_loop(state)
        
        # Guardar resultados
        save_results_to_file(state)
        
        print("\n🎉 Ejemplo completado exitosamente!")
        print("Revisa el archivo JSON generado para ver todos los detalles.")
    else:
        print("\n❌ El ejemplo no se pudo completar.")
    
    print("\n" + "=" * 50)
    print("Para usar este ejemplo con API keys reales:")
    print("1. Configura GOOGLE_API_KEY en tu .env")
    print("2. Opcionalmente configura TAVILY_API_KEY")
    print("3. Ejecuta: python examples/example_saas_landing.py")


if __name__ == "__main__":
    asyncio.run(main())
