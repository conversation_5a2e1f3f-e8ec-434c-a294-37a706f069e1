"""
Advanced logging system for the UI Design Agent.
Provides structured logging with different levels and contexts.
"""

import logging
import json
import os
import sys
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, Optional
from functools import wraps
import traceback


class AgentLogger:
    """
    Custom logger for the UI Design Agent with structured logging capabilities.
    """
    
    def __init__(self, name: str = "ui_agent", log_level: str = None):
        self.name = name
        self.log_level = log_level or os.getenv("LOG_LEVEL", "INFO")
        self.debug_mode = os.getenv("DEBUG", "false").lower() == "true"
        
        # Create logger
        self.logger = logging.getLogger(name)
        self.logger.setLevel(getattr(logging, self.log_level.upper()))
        
        # Prevent duplicate handlers
        if not self.logger.handlers:
            self._setup_handlers()
    
    def _setup_handlers(self):
        """Setup logging handlers for console and file output."""
        
        # Console handler
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(logging.INFO)
        
        # File handler
        log_dir = Path("logs")
        log_dir.mkdir(exist_ok=True)
        
        file_handler = logging.FileHandler(
            log_dir / f"{self.name}_{datetime.now().strftime('%Y%m%d')}.log"
        )
        file_handler.setLevel(logging.DEBUG)
        
        # Formatters
        if self.debug_mode:
            # Detailed format for debugging
            formatter = logging.Formatter(
                '%(asctime)s | %(levelname)-8s | %(name)s:%(funcName)s:%(lineno)d | %(message)s'
            )
        else:
            # Clean format for production
            formatter = logging.Formatter(
                '%(asctime)s | %(levelname)-8s | %(message)s'
            )
        
        console_handler.setFormatter(formatter)
        file_handler.setFormatter(formatter)
        
        self.logger.addHandler(console_handler)
        self.logger.addHandler(file_handler)
    
    def _format_message(self, message: str, context: Dict[str, Any] = None) -> str:
        """Format log message with optional context."""
        if context:
            context_str = json.dumps(context, default=str, separators=(',', ':'))
            return f"{message} | Context: {context_str}"
        return message
    
    def debug(self, message: str, context: Dict[str, Any] = None):
        """Log debug message."""
        self.logger.debug(self._format_message(message, context))
    
    def info(self, message: str, context: Dict[str, Any] = None):
        """Log info message."""
        self.logger.info(self._format_message(message, context))
    
    def warning(self, message: str, context: Dict[str, Any] = None):
        """Log warning message."""
        self.logger.warning(self._format_message(message, context))
    
    def error(self, message: str, context: Dict[str, Any] = None, exc_info: bool = False):
        """Log error message."""
        self.logger.error(self._format_message(message, context), exc_info=exc_info)
    
    def critical(self, message: str, context: Dict[str, Any] = None):
        """Log critical message."""
        self.logger.critical(self._format_message(message, context))
    
    def log_agent_phase(self, session_id: str, phase: str, details: Dict[str, Any] = None):
        """Log agent phase transitions."""
        context = {
            "session_id": session_id,
            "phase": phase,
            "timestamp": datetime.now().isoformat()
        }
        if details:
            context.update(details)
        
        self.info(f"Agent phase transition: {phase}", context)
    
    def log_api_call(self, api_name: str, success: bool, duration: float = None, details: Dict[str, Any] = None):
        """Log API calls with performance metrics."""
        context = {
            "api": api_name,
            "success": success,
            "duration_ms": round(duration * 1000, 2) if duration else None
        }
        if details:
            context.update(details)
        
        level = "info" if success else "error"
        message = f"API call {'succeeded' if success else 'failed'}: {api_name}"
        getattr(self, level)(message, context)
    
    def log_user_interaction(self, session_id: str, interaction_type: str, data: Dict[str, Any] = None):
        """Log user interactions."""
        context = {
            "session_id": session_id,
            "interaction": interaction_type,
            "timestamp": datetime.now().isoformat()
        }
        if data:
            context.update(data)
        
        self.info(f"User interaction: {interaction_type}", context)
    
    def log_performance(self, operation: str, duration: float, details: Dict[str, Any] = None):
        """Log performance metrics."""
        context = {
            "operation": operation,
            "duration_ms": round(duration * 1000, 2),
            "timestamp": datetime.now().isoformat()
        }
        if details:
            context.update(details)
        
        # Log as warning if operation is slow
        level = "warning" if duration > 5.0 else "info"
        message = f"Performance: {operation} took {duration:.2f}s"
        getattr(self, level)(message, context)


def log_function_calls(logger: AgentLogger):
    """
    Decorator to automatically log function calls with timing.
    """
    def decorator(func):
        @wraps(func)
        async def async_wrapper(*args, **kwargs):
            start_time = datetime.now()
            func_name = f"{func.__module__}.{func.__name__}"
            
            logger.debug(f"Starting function: {func_name}")
            
            try:
                result = await func(*args, **kwargs)
                duration = (datetime.now() - start_time).total_seconds()
                logger.log_performance(func_name, duration)
                return result
            except Exception as e:
                duration = (datetime.now() - start_time).total_seconds()
                logger.error(
                    f"Function failed: {func_name}",
                    context={
                        "error": str(e),
                        "duration_ms": round(duration * 1000, 2),
                        "traceback": traceback.format_exc()
                    }
                )
                raise
        
        @wraps(func)
        def sync_wrapper(*args, **kwargs):
            start_time = datetime.now()
            func_name = f"{func.__module__}.{func.__name__}"
            
            logger.debug(f"Starting function: {func_name}")
            
            try:
                result = func(*args, **kwargs)
                duration = (datetime.now() - start_time).total_seconds()
                logger.log_performance(func_name, duration)
                return result
            except Exception as e:
                duration = (datetime.now() - start_time).total_seconds()
                logger.error(
                    f"Function failed: {func_name}",
                    context={
                        "error": str(e),
                        "duration_ms": round(duration * 1000, 2),
                        "traceback": traceback.format_exc()
                    }
                )
                raise
        
        # Return appropriate wrapper based on function type
        import asyncio
        if asyncio.iscoroutinefunction(func):
            return async_wrapper
        else:
            return sync_wrapper
    
    return decorator


class PerformanceMonitor:
    """
    Monitor and log performance metrics for the UI Agent.
    """
    
    def __init__(self, logger: AgentLogger):
        self.logger = logger
        self.metrics = {}
    
    def start_timer(self, operation: str) -> str:
        """Start timing an operation."""
        timer_id = f"{operation}_{datetime.now().timestamp()}"
        self.metrics[timer_id] = {
            "operation": operation,
            "start_time": datetime.now(),
            "status": "running"
        }
        return timer_id
    
    def end_timer(self, timer_id: str, success: bool = True, details: Dict[str, Any] = None):
        """End timing an operation."""
        if timer_id not in self.metrics:
            self.logger.warning(f"Timer not found: {timer_id}")
            return
        
        metric = self.metrics[timer_id]
        duration = (datetime.now() - metric["start_time"]).total_seconds()
        
        metric.update({
            "end_time": datetime.now(),
            "duration": duration,
            "success": success,
            "status": "completed"
        })
        
        if details:
            metric.update(details)
        
        self.logger.log_performance(metric["operation"], duration, details)
        
        # Clean up completed metrics
        del self.metrics[timer_id]
    
    def get_active_timers(self) -> Dict[str, Dict[str, Any]]:
        """Get all currently active timers."""
        return {k: v for k, v in self.metrics.items() if v["status"] == "running"}


# Global logger instances
main_logger = AgentLogger("ui_agent_main")
tools_logger = AgentLogger("ui_agent_tools")
server_logger = AgentLogger("ui_agent_server")
graph_logger = AgentLogger("ui_agent_graph")

# Global performance monitor
performance_monitor = PerformanceMonitor(main_logger)


def get_logger(component: str = "main") -> AgentLogger:
    """
    Get a logger for a specific component.
    """
    loggers = {
        "main": main_logger,
        "tools": tools_logger,
        "server": server_logger,
        "graph": graph_logger
    }
    
    return loggers.get(component, main_logger)


def setup_logging():
    """
    Setup logging configuration for the entire application.
    """
    # Create logs directory
    log_dir = Path("logs")
    log_dir.mkdir(exist_ok=True)
    
    # Log startup information
    main_logger.info("UI Agent Constructor starting up", {
        "debug_mode": main_logger.debug_mode,
        "log_level": main_logger.log_level,
        "log_directory": str(log_dir.absolute())
    })
    
    return main_logger


if __name__ == "__main__":
    # Test the logging system
    logger = setup_logging()
    
    logger.info("Testing logging system")
    logger.debug("Debug message", {"test": True})
    logger.warning("Warning message")
    logger.error("Error message", {"error_code": 500})
    
    # Test performance monitoring
    timer_id = performance_monitor.start_timer("test_operation")
    import time
    time.sleep(0.1)
    performance_monitor.end_timer(timer_id, success=True, details={"test": "completed"})
    
    print("Logging system test completed. Check logs/ directory for output.")
