version: '3.8'

services:
  ui-agent-constructor:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: ui_agent_constructor
    environment:
      - GOOGLE_API_KEY=${GOOGLE_API_KEY}
      - TAVILY_API_KEY=${TAVILY_API_KEY}
      - MCP_SERVER_NAME=ui_agent_constructor
      - MCP_SERVER_VERSION=1.0.0
      - DEBUG=${DEBUG:-false}
      - LOG_LEVEL=${LOG_LEVEL:-INFO}
    volumes:
      # Mount source code for development (optional)
      - .:/app
    stdin_open: true
    tty: true
    restart: unless-stopped
    networks:
      - ui-agent-network

networks:
  ui-agent-network:
    driver: bridge
