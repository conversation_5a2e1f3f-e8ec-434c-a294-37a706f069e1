"""
Test script for the UI Design Agent.
This script tests the core functionality without requiring MCP client setup.
"""

import asyncio
import os
import json
from dotenv import load_dotenv

from state import session_manager, AgentState
from graph import ui_design_agent
from tools import web_inspiration_tool, image_analysis_tool, design_system_generator

# Load environment variables
load_dotenv()


async def test_basic_workflow():
    """
    Test the basic workflow of the UI Design Agent.
    """
    print("🧪 Testing UI Design Agent Basic Workflow\n")
    
    # Test 1: Create a new session
    print("1. Creating new design session...")
    session_id = "test-session-001"
    user_request = "Create a modern SaaS landing page with dark theme"
    
    state = session_manager.create_session(session_id, user_request)
    print(f"   ✅ Session created: {session_id}")
    print(f"   📝 Request: {user_request}")
    print(f"   📊 Initial phase: {state.current_phase}\n")
    
    # Test 2: Process through the agent workflow
    print("2. Processing through agent workflow...")
    
    try:
        # Step 1: Analyze request
        print("   🔍 Analyzing request...")
        updated_state = await ui_design_agent.process_request(state)
        session_manager.update_session(session_id, updated_state)
        print(f"   ✅ Analysis complete. Next phase: {updated_state.current_phase}")
        print(f"   🎯 Inspiration query: {updated_state.inspiration_query}")
        
        # Step 2: Research inspiration (if needed)
        if updated_state.current_phase == "research_inspiration":
            print("   🎨 Researching inspiration...")
            updated_state = await ui_design_agent.process_request(updated_state)
            session_manager.update_session(session_id, updated_state)
            print(f"   ✅ Inspiration research complete. Next phase: {updated_state.current_phase}")
            print(f"   📚 Found {len(updated_state.inspiration_sources)} inspiration sources")
        
        # Step 3: Create design system
        if updated_state.current_phase == "create_design_system":
            print("   🎯 Creating design system...")
            updated_state = await ui_design_agent.process_request(updated_state)
            session_manager.update_session(session_id, updated_state)
            print(f"   ✅ Design system created. Next phase: {updated_state.current_phase}")
            if updated_state.design_system:
                print(f"   🎨 Theme: {updated_state.design_system.theme}")
        
        # Step 4: Generate component code (simulate approval)
        if updated_state.current_phase == "await_design_approval":
            print("   👍 Simulating design system approval...")
            updated_state.design_system_approved = True
            updated_state.set_phase("generate_component_code")
            
            updated_state = await ui_design_agent.process_request(updated_state)
            session_manager.update_session(session_id, updated_state)
            print(f"   ✅ Component code generated. Next phase: {updated_state.current_phase}")
            if updated_state.component_code:
                code_preview = updated_state.component_code[:200] + "..." if len(updated_state.component_code) > 200 else updated_state.component_code
                print(f"   💻 Code preview: {code_preview}")
        
        print(f"\n✅ Workflow test completed successfully!")
        print(f"📊 Final state: {updated_state.current_phase}")
        print(f"🔄 Iterations: {updated_state.current_iteration}")
        
    except Exception as e:
        print(f"❌ Error during workflow: {str(e)}")
        return False
    
    return True


async def test_individual_tools():
    """
    Test individual tools separately.
    """
    print("\n🔧 Testing Individual Tools\n")
    
    # Test web inspiration tool
    print("1. Testing Web Inspiration Tool...")
    try:
        inspiration_sources = await web_inspiration_tool.browse_web_for_inspiration(
            "modern dashboard ui design", max_results=2
        )
        print(f"   ✅ Found {len(inspiration_sources)} inspiration sources")
        for i, source in enumerate(inspiration_sources, 1):
            print(f"   {i}. {source.title or 'Untitled'}: {source.url}")
    except Exception as e:
        print(f"   ⚠️  Web inspiration tool error: {str(e)}")
    
    # Test image analysis tool (with a sample URL)
    print("\n2. Testing Image Analysis Tool...")
    try:
        # Use a sample design image URL (this might fail if the URL is not accessible)
        sample_image_url = "https://via.placeholder.com/800x600/2563eb/ffffff?text=Sample+UI+Design"
        analysis = await image_analysis_tool.analyze_image_content(sample_image_url)
        
        if "error" in analysis:
            print(f"   ⚠️  Image analysis error: {analysis['error']}")
        else:
            print("   ✅ Image analysis completed")
            print(f"   📊 Analysis keys: {list(analysis.keys())}")
    except Exception as e:
        print(f"   ⚠️  Image analysis tool error: {str(e)}")
    
    # Test design system generator
    print("\n3. Testing Design System Generator...")
    try:
        sample_analyses = [
            {
                "composition": {"layout_type": "grid", "visual_hierarchy": "top-down"},
                "color_palette": {"primary_color": "#2563eb", "background_color": "#ffffff"},
                "design_style": {"overall_vibe": "minimalist"}
            }
        ]
        
        design_system = await design_system_generator.create_design_system(
            sample_analyses, "modern and clean"
        )
        
        if "error" in design_system:
            print(f"   ⚠️  Design system generator error: {design_system['error']}")
        else:
            print("   ✅ Design system generated")
            print(f"   🎨 Theme: {design_system.get('theme', 'Unknown')}")
    except Exception as e:
        print(f"   ⚠️  Design system generator error: {str(e)}")


async def test_feedback_loop():
    """
    Test the feedback and iteration functionality.
    """
    print("\n🔄 Testing Feedback Loop\n")
    
    # Get the test session
    session_id = "test-session-001"
    state = session_manager.get_session(session_id)
    
    if not state:
        print("❌ No test session found. Run basic workflow test first.")
        return False
    
    try:
        # Simulate user feedback
        feedback = "Make the colors more vibrant and add more spacing"
        print(f"💬 Providing feedback: {feedback}")
        
        updated_state = await ui_design_agent.process_feedback(state, feedback)
        session_manager.update_session(session_id, updated_state)
        
        print(f"✅ Feedback processed")
        print(f"📊 New phase: {updated_state.current_phase}")
        print(f"🔄 Iteration: {updated_state.current_iteration}")
        print(f"📝 Feedback history: {len(updated_state.feedback_history)} entries")
        
        return True
        
    except Exception as e:
        print(f"❌ Error during feedback test: {str(e)}")
        return False


async def main():
    """
    Main test runner.
    """
    print("🚀 UI Design Agent Test Suite")
    print("=" * 50)
    
    # Check environment variables
    if not os.getenv("GOOGLE_API_KEY"):
        print("⚠️  Warning: GOOGLE_API_KEY not set. Some tests may fail.")
    
    if not os.getenv("TAVILY_API_KEY"):
        print("⚠️  Warning: TAVILY_API_KEY not set. Web search will use fallback.")
    
    print()
    
    # Run tests
    tests_passed = 0
    total_tests = 3
    
    # Test 1: Basic workflow
    if await test_basic_workflow():
        tests_passed += 1
    
    # Test 2: Individual tools
    await test_individual_tools()  # This test is informational, doesn't count as pass/fail
    
    # Test 3: Feedback loop
    if await test_feedback_loop():
        tests_passed += 1
    
    # Summary
    print("\n" + "=" * 50)
    print(f"🏁 Test Summary: {tests_passed}/{total_tests} core tests passed")
    
    if tests_passed == total_tests:
        print("🎉 All core tests passed! The agent is ready for use.")
    else:
        print("⚠️  Some tests failed. Check the error messages above.")
    
    # Show active sessions
    sessions = session_manager.list_sessions()
    print(f"📊 Active sessions: {len(sessions)}")


if __name__ == "__main__":
    asyncio.run(main())
