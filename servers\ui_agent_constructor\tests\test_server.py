"""
Unit tests for the MCP server module.
"""

import pytest
import asyncio
import json
import sys
from pathlib import Path
from unittest.mock import Mock, patch, AsyncMock

# Add parent directory to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent))

from server import (
    handle_create_ui_design,
    handle_provide_feedback,
    handle_get_design_status,
    handle_list_active_sessions,
    format_agent_response
)
from state import AgentState, DesignSystem, session_manager
from mcp.types import TextContent


class TestMCPServerHandlers:
    """Test the MCP server handler functions."""
    
    def setup_method(self):
        """Setup for each test method."""
        # Clear any existing sessions
        session_manager.sessions.clear()
    
    @pytest.mark.asyncio
    async def test_handle_create_ui_design_success(self):
        """Test successful UI design creation."""
        arguments = {
            "request": "Create a modern button component",
            "session_id": "test-session-001"
        }
        
        # Mock the agent processing
        with patch('server.ui_design_agent.process_request') as mock_process:
            # Create a mock state that would be returned
            mock_state = AgentState(
                session_id="test-session-001",
                user_request="Create a modern button component"
            )
            mock_state.set_phase("research_inspiration")
            mock_process.return_value = mock_state
            
            result = await handle_create_ui_design(arguments)
            
            assert len(result) == 1
            assert isinstance(result[0], TextContent)
            assert "test-session-001" in result[0].text
            assert "Research Inspiration" in result[0].text
    
    @pytest.mark.asyncio
    async def test_handle_create_ui_design_empty_request(self):
        """Test UI design creation with empty request."""
        arguments = {"request": ""}
        
        result = await handle_create_ui_design(arguments)
        
        assert len(result) == 1
        assert isinstance(result[0], TextContent)
        assert "Error: Request description is required" in result[0].text
    
    @pytest.mark.asyncio
    async def test_handle_create_ui_design_auto_session_id(self):
        """Test UI design creation with auto-generated session ID."""
        arguments = {"request": "Create a card component"}
        
        with patch('server.ui_design_agent.process_request') as mock_process:
            mock_state = AgentState(
                session_id="auto-generated-id",
                user_request="Create a card component"
            )
            mock_process.return_value = mock_state
            
            result = await handle_create_ui_design(arguments)
            
            assert len(result) == 1
            assert isinstance(result[0], TextContent)
            # Should contain some session ID (auto-generated)
            assert "Session:" in result[0].text
    
    @pytest.mark.asyncio
    async def test_handle_provide_feedback_success(self):
        """Test successful feedback handling."""
        # First create a session
        session_id = "feedback-test-session"
        state = session_manager.create_session(session_id, "Create a button")
        
        arguments = {
            "session_id": session_id,
            "feedback": "Make it bigger and more colorful"
        }
        
        with patch('server.ui_design_agent.process_feedback') as mock_feedback:
            # Mock feedback processing
            state.add_feedback("Make it bigger and more colorful")
            state.next_iteration()
            mock_feedback.return_value = state
            
            result = await handle_provide_feedback(arguments)
            
            assert len(result) == 1
            assert isinstance(result[0], TextContent)
            assert session_id in result[0].text
    
    @pytest.mark.asyncio
    async def test_handle_provide_feedback_missing_params(self):
        """Test feedback handling with missing parameters."""
        # Test missing feedback
        result1 = await handle_provide_feedback({"session_id": "test"})
        assert "Error: Both feedback and session_id are required" in result1[0].text
        
        # Test missing session_id
        result2 = await handle_provide_feedback({"feedback": "test feedback"})
        assert "Error: Both feedback and session_id are required" in result2[0].text
    
    @pytest.mark.asyncio
    async def test_handle_provide_feedback_nonexistent_session(self):
        """Test feedback handling for non-existent session."""
        arguments = {
            "session_id": "nonexistent-session",
            "feedback": "Some feedback"
        }
        
        result = await handle_provide_feedback(arguments)
        
        assert len(result) == 1
        assert "Error: Session nonexistent-session not found" in result[0].text
    
    @pytest.mark.asyncio
    async def test_handle_get_design_status_success(self):
        """Test successful design status retrieval."""
        # Create a session
        session_id = "status-test-session"
        state = session_manager.create_session(session_id, "Create a form")
        state.set_phase("create_design_system")
        session_manager.update_session(session_id, state)
        
        arguments = {"session_id": session_id}
        
        result = await handle_get_design_status(arguments)
        
        assert len(result) == 1
        assert isinstance(result[0], TextContent)
        assert session_id in result[0].text
        assert "create_design_system" in result[0].text
    
    @pytest.mark.asyncio
    async def test_handle_get_design_status_missing_session_id(self):
        """Test design status retrieval with missing session ID."""
        result = await handle_get_design_status({})
        
        assert len(result) == 1
        assert "Error: session_id is required" in result[0].text
    
    @pytest.mark.asyncio
    async def test_handle_get_design_status_nonexistent_session(self):
        """Test design status retrieval for non-existent session."""
        arguments = {"session_id": "nonexistent-session"}
        
        result = await handle_get_design_status(arguments)
        
        assert len(result) == 1
        assert "Session nonexistent-session not found" in result[0].text
    
    @pytest.mark.asyncio
    async def test_handle_list_active_sessions_empty(self):
        """Test listing active sessions when none exist."""
        # Ensure no sessions exist
        session_manager.sessions.clear()
        
        result = await handle_list_active_sessions()
        
        assert len(result) == 1
        assert "No active design sessions" in result[0].text
    
    @pytest.mark.asyncio
    async def test_handle_list_active_sessions_with_sessions(self):
        """Test listing active sessions when some exist."""
        # Create multiple sessions
        session_ids = ["session-1", "session-2", "session-3"]
        for session_id in session_ids:
            session_manager.create_session(session_id, f"Request for {session_id}")
        
        result = await handle_list_active_sessions()
        
        assert len(result) == 1
        assert isinstance(result[0], TextContent)
        
        response_text = result[0].text
        assert "Active Design Sessions (3)" in response_text
        
        # Check that all session IDs are mentioned
        for session_id in session_ids:
            assert session_id in response_text


class TestFormatAgentResponse:
    """Test the format_agent_response function."""
    
    @pytest.mark.asyncio
    async def test_format_response_analyze_request_phase(self):
        """Test formatting response for analyze_request phase."""
        state = AgentState(
            session_id="test-session",
            user_request="Create a button"
        )
        state.set_phase("analyze_request")
        
        response = await format_agent_response(state)
        
        assert "test-session" in response
        assert "Analyze Request" in response
        assert "Iteration: 1" in response
    
    @pytest.mark.asyncio
    async def test_format_response_research_inspiration_phase(self):
        """Test formatting response for research_inspiration phase."""
        state = AgentState(
            session_id="test-session",
            user_request="Create a button"
        )
        state.set_phase("research_inspiration")
        state.inspiration_query = "modern button design"
        
        # Add some inspiration sources
        from state import InspirationSource
        source1 = InspirationSource(
            url="https://example.com/design1",
            title="Modern Button Design",
            description="A great button design example"
        )
        source2 = InspirationSource(
            url="https://example.com/design2",
            title="Creative Buttons"
        )
        state.inspiration_sources = [source1, source2]
        
        response = await format_agent_response(state)
        
        assert "Gathering Design Inspiration" in response
        assert "modern button design" in response
        assert "Modern Button Design" in response
        assert "https://example.com/design1" in response
        assert "Creative Buttons" in response
    
    @pytest.mark.asyncio
    async def test_format_response_design_system_phase(self):
        """Test formatting response for design system phase."""
        state = AgentState(
            session_id="test-session",
            user_request="Create a button"
        )
        state.set_phase("await_design_approval")
        
        # Add design system
        design_system = DesignSystem(
            theme="Modern Dark",
            color_palette={
                "primary": "#2563eb",
                "secondary": "#64748b",
                "accent": "#f59e0b",
                "background": "#0f172a",
                "text": "#f8fafc"
            },
            typography={
                "headerFont": "Inter",
                "bodyFont": "Inter",
                "baseFontSize": "16px"
            },
            layout={
                "gridUnit": 8,
                "borderRadius": "12px",
                "shadows": "0 8px 32px rgba(0, 0, 0, 0.3)"
            }
        )
        state.design_system = design_system
        
        response = await format_agent_response(state)
        
        assert "Design System Created" in response
        assert "Modern Dark" in response
        assert "#2563eb" in response
        assert "Inter" in response
        assert "12px" in response
        assert "Please review this design system" in response
    
    @pytest.mark.asyncio
    async def test_format_response_component_code_phase(self):
        """Test formatting response for component code phase."""
        state = AgentState(
            session_id="test-session",
            user_request="Create a button"
        )
        state.set_phase("await_feedback")
        
        # Add design system and component code
        design_system = DesignSystem(
            theme="Modern Minimal",
            color_palette={"primary": "#007bff"},
            typography={"headerFont": "Arial"},
            layout={"gridUnit": 4}
        )
        state.design_system = design_system
        state.component_code = """
import React from 'react';

const Button = ({ children, onClick }) => {
  return (
    <button 
      className="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
      onClick={onClick}
    >
      {children}
    </button>
  );
};

export default Button;
        """.strip()
        
        response = await format_agent_response(state)
        
        assert "Generated Component Code" in response
        assert "Modern Minimal" in response
        assert "import React" in response
        assert "const Button" in response
        assert "Please review the generated code" in response
    
    @pytest.mark.asyncio
    async def test_format_response_with_feedback_history(self):
        """Test formatting response with feedback history."""
        state = AgentState(
            session_id="test-session",
            user_request="Create a button"
        )
        
        # Add feedback
        state.add_feedback("Make it bigger")
        state.next_iteration()
        state.add_feedback("Change the color to blue")
        
        response = await format_agent_response(state)
        
        assert "Feedback History:" in response
        assert "Make it bigger" in response
        assert "Change the color to blue" in response
        assert "Iteration 1:" in response
        assert "Iteration 2:" in response
    
    @pytest.mark.asyncio
    async def test_format_response_complete_phase(self):
        """Test formatting response for complete phase."""
        state = AgentState(
            session_id="test-session",
            user_request="Create a button"
        )
        state.set_phase("complete")
        state.is_complete = True
        
        response = await format_agent_response(state)
        
        assert "Design Complete!" in response
        assert "completed successfully" in response
    
    @pytest.mark.asyncio
    async def test_format_response_error_phase(self):
        """Test formatting response for error phase."""
        state = AgentState(
            session_id="test-session",
            user_request="Create a button"
        )
        state.set_phase("error")
        
        response = await format_agent_response(state)
        
        assert "Error" in response
        assert "An error occurred" in response


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
