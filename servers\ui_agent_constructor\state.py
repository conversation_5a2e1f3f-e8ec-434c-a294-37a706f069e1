"""
State management for the UI Design Agent.
Defines the AgentState structure that persists throughout the design workflow.
"""

from typing import List, Dict, Optional, Any
from pydantic import BaseModel, Field
from datetime import datetime


class DesignSystem(BaseModel):
    """Represents a complete design system generated from inspiration."""
    theme: str = Field(description="Theme name (e.g., 'Dark Minimalist', 'Vibrant Glassmorphism')")
    color_palette: Dict[str, str] = Field(description="Color palette with primary, secondary, accent, background, text")
    typography: Dict[str, str] = Field(description="Typography settings with headerFont, bodyFont, baseFontSize")
    layout: Dict[str, Any] = Field(description="Layout settings with gridUnit, borderRadius, shadows")


class InspirationSource(BaseModel):
    """Represents a single source of design inspiration."""
    url: str = Field(description="URL of the inspiration source")
    title: Optional[str] = Field(default=None, description="Title of the page/design")
    description: Optional[str] = Field(default=None, description="Brief description")
    analysis: Optional[Dict[str, Any]] = Field(default=None, description="AI analysis of the design")
    timestamp: datetime = Field(default_factory=datetime.now)


class FeedbackEntry(BaseModel):
    """Represents user feedback on generated designs."""
    feedback_text: str = Field(description="User's feedback text")
    timestamp: datetime = Field(default_factory=datetime.now)
    iteration_number: int = Field(description="Which iteration this feedback refers to")


class AgentState(BaseModel):
    """
    Complete state of the UI Design Agent throughout the design process.
    This follows the Three Pillars approach from RULES.md.
    """
    
    # User Request and Context
    user_request: str = Field(description="Original user request for UI design")
    session_id: str = Field(description="Unique session identifier")
    
    # Pilar 1: Inspiration Phase
    inspiration_query: Optional[str] = Field(default=None, description="Search query used for inspiration")
    inspiration_sources: List[InspirationSource] = Field(default_factory=list, description="Collected inspiration sources")
    inspiration_complete: bool = Field(default=False, description="Whether inspiration gathering is complete")
    
    # Pilar 2: Systematization Phase
    design_system: Optional[DesignSystem] = Field(default=None, description="Generated design system")
    design_system_approved: bool = Field(default=False, description="Whether user approved the design system")
    
    # Pilar 3: Composition Phase
    component_code: Optional[str] = Field(default=None, description="Generated component code")
    component_type: Optional[str] = Field(default=None, description="Type of component (e.g., 'landing_page', 'dashboard')")
    
    # Feedback and Iteration
    feedback_history: List[FeedbackEntry] = Field(default_factory=list, description="History of user feedback")
    current_iteration: int = Field(default=1, description="Current iteration number")
    
    # Workflow State
    current_phase: str = Field(default="analyze_request", description="Current phase of the workflow")
    is_complete: bool = Field(default=False, description="Whether the design process is complete")
    
    # Metadata
    created_at: datetime = Field(default_factory=datetime.now)
    updated_at: datetime = Field(default_factory=datetime.now)
    
    def update_timestamp(self):
        """Update the updated_at timestamp."""
        self.updated_at = datetime.now()
    
    def add_feedback(self, feedback_text: str):
        """Add user feedback to the history."""
        feedback = FeedbackEntry(
            feedback_text=feedback_text,
            iteration_number=self.current_iteration
        )
        self.feedback_history.append(feedback)
        self.update_timestamp()
    
    def next_iteration(self):
        """Move to the next iteration."""
        self.current_iteration += 1
        self.update_timestamp()
    
    def set_phase(self, phase: str):
        """Set the current workflow phase."""
        self.current_phase = phase
        self.update_timestamp()


class SessionManager:
    """Manages multiple agent sessions in memory."""
    
    def __init__(self):
        self.sessions: Dict[str, AgentState] = {}
    
    def create_session(self, session_id: str, user_request: str) -> AgentState:
        """Create a new agent session."""
        state = AgentState(
            session_id=session_id,
            user_request=user_request
        )
        self.sessions[session_id] = state
        return state
    
    def get_session(self, session_id: str) -> Optional[AgentState]:
        """Get an existing session."""
        return self.sessions.get(session_id)
    
    def update_session(self, session_id: str, state: AgentState):
        """Update an existing session."""
        state.update_timestamp()
        self.sessions[session_id] = state
    
    def delete_session(self, session_id: str):
        """Delete a session."""
        if session_id in self.sessions:
            del self.sessions[session_id]
    
    def list_sessions(self) -> List[str]:
        """List all active session IDs."""
        return list(self.sessions.keys())


# Global session manager instance
session_manager = SessionManager()
